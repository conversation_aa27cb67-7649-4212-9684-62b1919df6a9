@echo off
echo ========================================
echo 阅读心得分享平台 - 性能优化启动脚本
echo ========================================

echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请安装JDK 17或更高版本
    pause
    exit /b 1
)

echo.
echo 正在检查Maven Wrapper...
if not exist "mvnw.cmd" (
    echo 错误：未找到Maven Wrapper，请确保在项目根目录运行
    pause
    exit /b 1
)

echo.
echo 正在清理并编译项目...
call mvnw.cmd clean compile -q

echo.
echo 正在启动应用（性能优化模式）...
echo 使用以下JVM优化参数：
echo - 初始堆内存: 512MB
echo - 最大堆内存: 1024MB
echo - 垃圾收集器: G1GC
echo - 最大GC暂停时间: 200ms
echo - 启用压缩普通对象指针
echo.

call mvnw.cmd spring-boot:run ^
    -Dspring-boot.run.jvmArguments="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -server"

if %errorlevel% neq 0 (
    echo.
    echo 启动失败！请检查：
    echo 1. 数据库是否已启动
    echo 2. 端口8080是否被占用
    echo 3. 配置文件是否正确
    echo.
    pause
)
