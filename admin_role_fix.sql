-- 修复admin用户角色关联问题
USE reading_share;

-- 1. 检查当前admin用户状态
SELECT '=== 当前admin用户状态 ===' as info;
SELECT id, username, enabled, nickname FROM users WHERE username = 'admin';

-- 2. 检查admin用户的角色关联
SELECT '=== admin用户当前角色 ===' as info;
SELECT ur.user_id, ur.role_id, r.name as role_name
FROM user_roles ur
JOIN roles r ON ur.role_id = r.id
WHERE ur.user_id = (SELECT id FROM users WHERE username = 'admin');

-- 3. 删除admin用户现有的角色关联（如果有的话）
DELETE FROM user_roles WHERE user_id = (SELECT id FROM users WHERE username = 'admin');

-- 4. 重新为admin用户分配ROLE_ADMIN角色
INSERT INTO user_roles (user_id, role_id) 
VALUES (
    (SELECT id FROM users WHERE username = 'admin'),
    (SELECT id FROM roles WHERE name = 'ROLE_ADMIN')
);

-- 5. 确保admin用户密码正确
UPDATE users 
SET password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6',
    enabled = 1,
    nickname = '管理员'  -- 使用更短的昵称避免长度问题
WHERE username = 'admin';

-- 6. 验证修复结果
SELECT '=== 修复后验证 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, r.name as role_name
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

SELECT '=== 修复完成 ===' as info;
SELECT 'admin用户已修复，密码为admin123' as result;
