-- 管理员登录问题修复脚本 v2
-- 兼容不同的表结构
USE reading_share;

-- 1. 检查当前数据库状态
SELECT '=== 检查用户表 ===' as info;
SELECT id, username, enabled, password, nickname FROM users;

SELECT '=== 检查角色表 ===' as info;
SELECT * FROM roles;

SELECT '=== 检查用户角色关联 ===' as info;
SELECT u.username, r.name as role_name 
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id;

-- 2. 确保基础角色存在（不使用create_time和update_time字段）
INSERT IGNORE INTO roles (name, description) 
VALUES 
('ROLE_ADMIN', '管理员'),
('ROLE_USER', '普通用户'),
('ROLE_EDITOR', '编辑者');

-- 3. 删除现有的admin用户（如果存在）
DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');
DELETE FROM users WHERE username = 'admin';

-- 4. 创建新的admin用户（不使用create_time和update_time字段）
-- 密码是 admin123 的BCrypt加密结果
INSERT INTO users (username, password, nickname, enabled) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1);

-- 5. 为admin用户分配管理员角色
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'ROLE_ADMIN';

-- 6. 验证结果
SELECT '=== 验证admin用户 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, r.name as role_name
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

-- 7. 创建一个测试用户（如果不存在）
INSERT IGNORE INTO users (username, password, nickname, enabled) 
VALUES ('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '测试用户', 1);

-- 为测试用户分配普通用户角色
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'testuser' AND r.name = 'ROLE_USER';

SELECT '=== 修复完成 ===' as info;
SELECT '管理员账号: admin' as info;
SELECT '管理员密码: admin123' as info;
SELECT '测试账号: testuser' as info;
SELECT '测试密码: admin123' as info;
