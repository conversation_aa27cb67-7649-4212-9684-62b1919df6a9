-- 基于正确数据库结构的管理员登录修复脚本
USE reading_share;

-- 1. 检查当前数据库状态
SELECT '=== 检查用户表 ===' as info;
SELECT id, username, enabled, nickname FROM users;

SELECT '=== 检查角色表 ===' as info;
SELECT * FROM roles;

SELECT '=== 检查用户角色关联 ===' as info;
SELECT u.username, r.name as role_name 
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id;

-- 2. 检查admin用户的详细信息
SELECT '=== admin用户详细信息 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, 
       LENGTH(u.password) as password_length,
       SUBSTRING(u.password, 1, 10) as password_prefix
FROM users u 
WHERE u.username = 'admin';

-- 3. 检查admin用户的角色
SELECT '=== admin用户角色 ===' as info;
SELECT u.username, r.name as role_name, r.description
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

-- 4. 如果admin用户不存在，重新创建
INSERT IGNORE INTO users (username, password, nickname, enabled) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1);

-- 5. 确保admin用户的密码和状态正确
UPDATE users 
SET password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6',
    enabled = 1,
    nickname = '系统管理员'
WHERE username = 'admin';

-- 6. 确保ROLE_ADMIN角色存在
INSERT IGNORE INTO roles (name, description) 
VALUES ('ROLE_ADMIN', '管理员');

-- 7. 确保admin用户有ROLE_ADMIN角色
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'ROLE_ADMIN';

-- 8. 最终验证
SELECT '=== 修复后验证 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, r.name as role_name
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

-- 9. 验证密码格式
SELECT '=== 密码验证 ===' as info;
SELECT username, 
       CASE 
           WHEN password LIKE '$2a$%' THEN '✅ BCrypt格式正确'
           ELSE '❌ 密码格式错误'
       END as password_status,
       CASE 
           WHEN enabled = 1 THEN '✅ 用户已启用'
           ELSE '❌ 用户被禁用'
       END as enabled_status
FROM users 
WHERE username = 'admin';

SELECT '=== 修复完成 ===' as info;
SELECT '管理员账号: admin' as login_info;
SELECT '管理员密码: admin123' as login_info;
SELECT '请重启应用程序后尝试登录' as next_step;
