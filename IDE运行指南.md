# IDE运行指南 - 阅读心得分享项目

## 推荐方案: 使用IDE运行 (无需配置Maven环境)

### 1. IntelliJ IDEA (推荐)

#### 1.1 导入项目
1. 打开IntelliJ IDEA
2. 选择 "Open" 或 "Import Project"
3. 选择项目根目录 (包含pom.xml的目录)
4. 选择 "Import project from external model" → "Maven"
5. 点击 "Next" 直到完成

#### 1.2 等待依赖下载
- IDEA会自动下载Maven依赖
- 等待右下角进度条完成
- 如果网络慢，可能需要几分钟

#### 1.3 配置数据库
1. 确保MySQL已启动
2. 创建数据库: `CREATE DATABASE reading_share CHARACTER SET utf8mb4;`
3. 导入数据: `mysql -u root -p reading_share < reading_share.sql`
4. 修改 `src/main/resources/application.properties` 中的数据库密码

#### 1.4 运行项目
1. 找到 `src/main/java/com/coding24h/reading_share/ReadingShareApplication.java`
2. 右键文件 → "Run 'ReadingShareApplication'"
3. 或点击类名旁边的绿色三角形按钮

#### 1.5 访问应用
- 等待控制台显示 "Started ReadingShareApplication"
- 浏览器访问: http://localhost:8080/test/page

### 2. Eclipse

#### 2.1 导入项目
1. 打开Eclipse
2. File → Import → Existing Maven Projects
3. 选择项目根目录
4. 点击 "Finish"

#### 2.2 运行项目
1. 右键项目 → Run As → Spring Boot App
2. 或右键 `ReadingShareApplication.java` → Run As → Java Application

### 3. Visual Studio Code

#### 3.1 安装插件
1. 安装 "Extension Pack for Java"
2. 安装 "Spring Boot Extension Pack"

#### 3.2 运行项目
1. 打开项目文件夹
2. 按 F5 或 Ctrl+F5
3. 选择 "Java" 环境

## 常见问题解决

### 问题1: 依赖下载失败
**解决方案:**
1. 检查网络连接
2. 在IDE中刷新Maven项目:
   - IDEA: 右键项目 → Maven → Reload project
   - Eclipse: 右键项目 → Maven → Update Project

### 问题2: JDK版本问题
**解决方案:**
1. 确保使用JDK 17+
2. 在IDE中设置项目JDK:
   - IDEA: File → Project Structure → Project → Project SDK
   - Eclipse: 右键项目 → Properties → Java Build Path → Libraries

### 问题3: 编码问题
**解决方案:**
1. 设置IDE编码为UTF-8:
   - IDEA: File → Settings → Editor → File Encodings
   - Eclipse: Window → Preferences → General → Workspace → Text file encoding

### 问题4: 数据库连接失败
**解决方案:**
1. 检查MySQL服务状态
2. 验证数据库配置
3. 测试数据库连接:
```sql
mysql -u root -p
USE reading_share;
SHOW TABLES;
```

## 验证步骤

### 1. 启动成功标志
控制台应显示类似信息:
```
Started ReadingShareApplication in X.XXX seconds
```

### 2. 测试访问
- 测试页面: http://localhost:8080/test/page
- 首页: http://localhost:8080/
- 登录页面: http://localhost:8080/login

### 3. 功能测试
1. 访问测试页面，确认系统运行正常
2. 尝试注册新用户
3. 使用管理员账号登录 (admin/admin123)
4. 发布一篇心得测试

## 调试技巧

### 1. 查看控制台日志
- 注意红色错误信息
- 查看启动过程中的警告

### 2. 设置断点调试
- 在关键方法设置断点
- 使用Debug模式运行

### 3. 检查配置
- 确认application.properties配置正确
- 检查数据库连接参数

## 项目结构说明

```
src/main/java/com/coding24h/reading_share/
├── ReadingShareApplication.java    # 主启动类
├── config/                        # 配置类
│   ├── SecurityConfig.java       # 安全配置
│   └── WebConfig.java            # Web配置
├── controller/                    # 控制器层
├── entity/                       # 实体层 (替代model)
├── mapper/                       # 数据访问层
├── security/                     # 安全相关
└── service/                      # 服务层
    └── impl/                     # 服务实现
```

## 成功运行的标志

1. **控制台无错误**: 启动过程中没有红色ERROR信息
2. **端口监听**: 显示 "Tomcat started on port(s): 8080"
3. **应用启动**: 显示 "Started ReadingShareApplication"
4. **页面访问**: 能够正常访问测试页面
5. **数据库连接**: 能够正常登录和操作

如果遇到问题，请查看控制台的完整错误信息，这样可以更准确地定位和解决问题。
