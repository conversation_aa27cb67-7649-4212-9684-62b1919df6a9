# 阅读心得分享项目 - 问题诊断和解决方案

## 当前问题分析

根据错误信息 "无法将'mvn'项识别为cmdlet"，主要问题是Maven环境配置问题。

## 解决方案

### 方案1: 安装和配置Maven (推荐)

#### 1.1 下载Maven
- 访问 https://maven.apache.org/download.cgi
- 下载 apache-maven-3.9.x-bin.zip

#### 1.2 安装Maven
```bash
# 解压到目录，例如: C:\Program Files\Apache\maven
# 设置环境变量:
MAVEN_HOME = C:\Program Files\Apache\maven
PATH = %PATH%;%MAVEN_HOME%\bin
```

#### 1.3 验证安装
```bash
mvn -version
```

#### 1.4 运行项目
```bash
mvn clean compile
mvn spring-boot:run
```

### 方案2: 使用IDE运行 (最简单)

#### 2.1 IntelliJ IDEA
1. 打开项目文件夹
2. 等待Maven依赖下载完成
3. 找到 `ReadingShareApplication.java`
4. 右键 → Run 'ReadingShareApplication'

#### 2.2 Eclipse
1. Import → Existing Maven Projects
2. 选择项目文件夹
3. 等待依赖下载
4. 右键项目 → Run As → Spring Boot App

#### 2.3 VS Code
1. 安装Java Extension Pack
2. 打开项目文件夹
3. 按F5运行

### 方案3: 使用Spring Boot CLI

#### 3.1 安装Spring Boot CLI
```bash
# 使用Chocolatey (Windows)
choco install springbootcli

# 或下载并配置环境变量
```

#### 3.2 运行
```bash
spring run src/main/java/com/coding24h/reading_share/ReadingShareApplication.java
```

## 常见问题和解决方案

### 问题1: 数据库连接失败
```
错误: Could not create connection to database server
```

**解决方案:**
1. 确保MySQL服务已启动
2. 检查数据库配置 `application.properties`:
```properties
spring.datasource.url=*************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password  # 修改为实际密码
```
3. 创建数据库:
```sql
CREATE DATABASE reading_share CHARACTER SET utf8mb4;
```
4. 导入数据:
```bash
mysql -u root -p reading_share < reading_share.sql
```

### 问题2: 端口被占用
```
错误: Port 8080 was already in use
```

**解决方案:**
1. 修改端口 `application.properties`:
```properties
server.port=8081
```
2. 或关闭占用8080端口的程序

### 问题3: 编译错误
```
错误: 程序包不存在 / 找不到符号
```

**解决方案:**
1. 清理并重新编译:
```bash
mvn clean compile
```
2. 检查JDK版本 (需要17+):
```bash
java -version
```
3. 刷新IDE项目依赖

### 问题4: 启动后访问404
```
错误: Whitelabel Error Page
```

**解决方案:**
1. 检查Controller路径映射
2. 访问正确的URL:
   - 首页: http://localhost:8080/
   - 测试页面: http://localhost:8080/test/page
   - 登录页面: http://localhost:8080/login

### 问题5: 登录失败
```
错误: 用户名或密码错误
```

**解决方案:**
1. 使用默认管理员账号:
   - 用户名: admin
   - 密码: admin123
2. 检查数据库中用户数据是否正确导入

## 项目验证步骤

### 1. 基础验证
```bash
# 1. 检查Java版本
java -version

# 2. 检查Maven版本 (如果使用Maven)
mvn -version

# 3. 检查MySQL服务
# Windows: services.msc 查看MySQL服务状态
# 或命令行: net start mysql
```

### 2. 数据库验证
```sql
-- 连接MySQL
mysql -u root -p

-- 检查数据库
SHOW DATABASES;
USE reading_share;
SHOW TABLES;

-- 检查管理员用户
SELECT * FROM users WHERE username = 'admin';
```

### 3. 应用验证
1. 启动应用
2. 访问 http://localhost:8080/test/page
3. 检查系统状态
4. 尝试登录管理员账号

## 调试技巧

### 1. 查看详细日志
在 `application.properties` 中添加:
```properties
logging.level.root=DEBUG
logging.level.com.coding24h.reading_share=DEBUG
```

### 2. 检查依赖
```bash
mvn dependency:tree
```

### 3. 跳过测试启动
```bash
mvn spring-boot:run -Dmaven.test.skip=true
```

## 联系支持

如果以上方案都无法解决问题，请提供:
1. 完整的错误日志
2. Java版本信息
3. 操作系统信息
4. 使用的IDE或工具
5. 具体的操作步骤

这样可以更准确地诊断和解决问题。
