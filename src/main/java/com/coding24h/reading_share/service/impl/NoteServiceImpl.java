package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.mapper.NoteMapper;
import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.service.NoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

@Service
public class NoteServiceImpl implements NoteService {

    @Autowired
    private NoteMapper noteMapper;
    
    private static final String UPLOAD_DIR = "uploads/covers/";

    @Override
    public Note findById(Integer id) {
        return noteMapper.findById(id);
    }

    @Override
    public List<Note> findAll() {
        return noteMapper.findAll();
    }

    @Override
    public List<Note> findAllPublished() {
        return noteMapper.findAllPublished();
    }

    @Override
    public List<Note> findAllPublishedWithPaging(int page, int size) {
        int offset = page * size;
        return noteMapper.findAllPublishedWithPaging(offset, size);
    }

    @Override
    public List<Note> findByUserId(Integer userId) {
        return noteMapper.findByUserId(userId);
    }

    @Override
    public List<Note> findByCategoryId(Integer categoryId) {
        return noteMapper.findByCategoryId(categoryId);
    }

    @Override
    public List<Note> findByCategoryIdWithPaging(Integer categoryId, int page, int size) {
        int offset = page * size;
        return noteMapper.findByCategoryIdWithPaging(categoryId, offset, size);
    }

    @Override
    public int countAllPublished() {
        return noteMapper.countAllPublished();
    }

    @Override
    public int countByCategoryIdPublished(Integer categoryId) {
        return noteMapper.countByCategoryIdPublished(categoryId);
    }

    @Override
    public List<Note> findLatest(int limit) {
        return noteMapper.findLatest(limit);
    }

    @Override
    public List<Note> findRelated(Integer categoryId, Integer noteId, int limit) {
        return noteMapper.findRelated(categoryId, noteId, limit);
    }

    @Override
    @Transactional
    public void create(Note note) {
        noteMapper.insert(note);
    }

    @Override
    @Transactional
    public void update(Note note) {
        noteMapper.update(note);
    }

    @Override
    @Transactional
    public void delete(Integer id) {
        noteMapper.delete(id);
    }

    @Override
    @Transactional
    public void incrementViewCount(Integer id) {
        noteMapper.incrementViewCount(id);
    }

    @Override
    public String uploadCoverImage(MultipartFile file) {
        if (file.isEmpty()) {
            return null;
        }
        
        try {
            // 创建上传目录
            File uploadDir = new File(UPLOAD_DIR);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID().toString() + extension;
            
            // 保存文件
            Path filePath = Paths.get(UPLOAD_DIR + filename);
            Files.copy(file.getInputStream(), filePath);
            
            return "/uploads/covers/" + filename;
        } catch (IOException e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public boolean isNoteOwner(Integer noteId, Integer userId) {
        Note note = noteMapper.findById(noteId);
        return note != null && note.getUserId().equals(userId);
    }
}
