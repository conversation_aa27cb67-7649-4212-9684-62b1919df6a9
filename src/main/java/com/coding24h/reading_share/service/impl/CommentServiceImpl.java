package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.mapper.CommentMapper;
import com.coding24h.reading_share.entity.Comment;
import com.coding24h.reading_share.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CommentServiceImpl implements CommentService {

    @Autowired
    private CommentMapper commentMapper;

    @Override
    public Comment findById(Integer id) {
        return commentMapper.findById(id);
    }
    
    @Override
    public List<Comment> findByNoteId(Integer noteId) {
        return commentMapper.findByNoteId(noteId);
    }
    
    @Override
    public List<Comment> findByUserId(Integer userId) {
        return commentMapper.findByUserId(userId);
    }
    
    @Override
    @Transactional
    public void create(Comment comment) {
        commentMapper.insert(comment);
    }
    
    @Override
    @Transactional
    public void update(Comment comment) {
        commentMapper.update(comment);
    }
    
    @Override
    @Transactional
    public void delete(Integer id) {
        commentMapper.delete(id);
    }

    @Override
    public List<Comment> findByParentId(Integer parentId) {
        return commentMapper.findByParentId(parentId);
    }

    @Override
    public boolean isCommentOwner(Integer commentId, Integer userId) {
        Comment comment = commentMapper.findById(commentId);
        return comment != null && comment.getUserId().equals(userId);
    }
}