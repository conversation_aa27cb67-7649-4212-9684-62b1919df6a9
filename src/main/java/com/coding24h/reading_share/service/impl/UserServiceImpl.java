package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.mapper.UserMapper;
import com.coding24h.reading_share.mapper.RoleMapper;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.Role;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User findById(Integer id) {
        return userMapper.findById(id);
    }
    
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    @Override
    public List<User> findAll() {
        return userMapper.findAll();
    }
    
    @Override
    @Transactional
    public void register(User user) {
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setEnabled(true);
        
        // 保存用户
        userMapper.insert(user);
        
        // 分配默认角色 (ROLE_USER)
        Role userRole = roleMapper.findByName("ROLE_USER");
        roleMapper.assignRoleToUser(user.getId(), userRole.getId());
    }
    
    @Override
    @Transactional
    public void update(User user) {
        userMapper.update(user);
    }
    
    @Override
    @Transactional
    public void delete(Integer id) {
        userMapper.delete(id);
    }
    
    @Override
    @Transactional
    public void assignRole(Integer userId, Integer roleId) {
        roleMapper.assignRoleToUser(userId, roleId);
    }
    
    @Override
    @Transactional
    public void removeRole(Integer userId, Integer roleId) {
        roleMapper.removeRoleFromUser(userId, roleId);
    }
}