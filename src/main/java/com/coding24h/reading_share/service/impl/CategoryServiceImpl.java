package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.mapper.CategoryMapper;
import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public Category findById(Integer id) {
        return categoryMapper.findById(id);
    }

    @Override
    public List<Category> findAll() {
        return categoryMapper.findAll();
    }

    @Override
    @Transactional
    public void create(Category category) {
        categoryMapper.insert(category);
    }

    @Override
    @Transactional
    public void update(Category category) {
        categoryMapper.update(category);
    }

    @Override
    @Transactional
    public void delete(Integer id) {
        categoryMapper.delete(id);
    }
}
