package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.Note;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface NoteService {
    
    Note findById(Integer id);
    
    List<Note> findAll();
    
    List<Note> findAllPublished();

    List<Note> findAllPublishedWithPaging(int page, int size);

    List<Note> findByUserId(Integer userId);

    List<Note> findByCategoryId(Integer categoryId);

    List<Note> findByCategoryIdWithPaging(Integer categoryId, int page, int size);

    int countAllPublished();

    int countByCategoryIdPublished(Integer categoryId);
    
    List<Note> findLatest(int limit);
    
    List<Note> findRelated(Integer categoryId, Integer noteId, int limit);
    
    void create(Note note);
    
    void update(Note note);
    
    void delete(Integer id);
    
    void incrementViewCount(Integer id);
    
    String uploadCoverImage(MultipartFile file);
    
    boolean isNoteOwner(Integer noteId, Integer userId);
}