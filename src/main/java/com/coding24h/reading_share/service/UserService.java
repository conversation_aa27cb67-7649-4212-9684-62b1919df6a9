package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.User;
import java.util.List;

public interface UserService {
    
    User findById(Integer id);
    
    User findByUsername(String username);
    
    List<User> findAll();
    
    void register(User user);
    
    void update(User user);
    
    void delete(Integer id);
    
    void assignRole(Integer userId, Integer roleId);
    
    void removeRole(Integer userId, Integer roleId);
}