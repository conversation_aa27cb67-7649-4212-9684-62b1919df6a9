package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.Comment;
import java.util.List;

public interface CommentService {
    
    Comment findById(Integer id);
    
    List<Comment> findByNoteId(Integer noteId);
    
    List<Comment> findByUserId(Integer userId);
    
    List<Comment> findByParentId(Integer parentId);
    
    void create(Comment comment);
    
    void update(Comment comment);
    
    void delete(Integer id);
    
    boolean isCommentOwner(Integer commentId, Integer userId);
}
