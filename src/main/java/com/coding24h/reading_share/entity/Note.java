package com.coding24h.reading_share.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class Note {
    private Integer id;
    private String title;
    private String content;
    private String coverImage;
    private String bookName;
    private String bookAuthor;
    private Integer categoryId;
    private Integer userId;
    private String status; // "draft" 或 "published"
    private Integer viewCount;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联对象，非数据库字段
    private User user;
    private Category category;
}
