package com.coding24h.reading_share.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class Comment {
    private Integer id;
    private String content;
    private Integer noteId;
    private Integer userId;
    private Integer parentId; // 父评论ID，用于回复功能
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联对象，非数据库字段
    private User user;
    private Note note;
    private Comment parentComment;
}
