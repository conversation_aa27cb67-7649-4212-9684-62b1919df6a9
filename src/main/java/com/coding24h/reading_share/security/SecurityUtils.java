package com.coding24h.reading_share.security;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.mapper.UserMapper;
import com.coding24h.reading_share.mapper.NoteMapper;
import com.coding24h.reading_share.mapper.CommentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

@Component
public class SecurityUtils {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private NoteMapper noteMapper;

    @Autowired
    private CommentMapper commentMapper;

    /**
     * 获取当前登录用户
     */
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            UserDetails userDetails = (UserDetails) principal;
            return userMapper.findByUsername(userDetails.getUsername());
        }
        
        return null;
    }
    
    /**
     * 检查当前用户是否是指定心得的作者或管理员
     */
    public boolean isNoteOwnerOrAdmin(Integer noteId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        // 检查是否是管理员
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        if (isAdmin) {
            return true;
        }

        // 检查是否是作者
        Integer currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        // 直接查询数据库检查是否是心得作者
        com.coding24h.reading_share.entity.Note note = noteMapper.findById(noteId);
        return note != null && note.getUserId().equals(currentUserId);
    }

    /**
     * 检查当前用户是否是管理员
     */
    public boolean isAdmin() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        return authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
    }

    /**
     * 检查当前用户是否是指定评论的作者或管理员
     */
    public boolean isCommentOwnerOrAdmin(Integer commentId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        // 检查是否是管理员
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        if (isAdmin) {
            return true;
        }

        // 检查是否是作者
        Integer currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        // 直接查询数据库检查是否是评论作者
        com.coding24h.reading_share.entity.Comment comment = commentMapper.findById(commentId);
        return comment != null && comment.getUserId().equals(currentUserId);
    }
    
    /**
     * 获取当前用户ID
     */
    public Integer getCurrentUserId() {
        User currentUser = getCurrentUser();
        return currentUser != null ? currentUser.getId() : null;
    }
}