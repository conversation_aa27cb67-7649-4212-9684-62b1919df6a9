package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Note;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NoteMapper {
    
    Note findById(Integer id);
    
    List<Note> findAll();
    
    List<Note> findAllPublished();

    List<Note> findAllPublishedWithPaging(@Param("offset") int offset, @Param("limit") int limit);

    List<Note> findByUserId(Integer userId);

    List<Note> findByCategoryId(Integer categoryId);

    List<Note> findByCategoryIdWithPaging(@Param("categoryId") Integer categoryId,
                                         @Param("offset") int offset,
                                         @Param("limit") int limit);

    int countAllPublished();

    int countByCategoryIdPublished(Integer categoryId);
    
    List<Note> findLatest(int limit);
    
    List<Note> findRelated(@Param("categoryId") Integer categoryId, 
                          @Param("noteId") Integer noteId, 
                          @Param("limit") int limit);
    
    void insert(Note note);
    
    void update(Note note);
    
    void delete(Integer id);
    
    void incrementViewCount(Integer id);
    
    int countByUserId(Integer userId);
    
    int countByCategoryId(Integer categoryId);
}