package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface RoleMapper {
    
    Role findById(Integer id);
    
    Role findByName(String name);
    
    List<Role> findAll();
    
    List<Role> findByUserId(Integer userId);
    
    void insert(Role role);
    
    void update(Role role);
    
    void delete(Integer id);
    
    void assignRoleToUser(@Param("userId") Integer userId, @Param("roleId") Integer roleId);
    
    void removeRoleFromUser(@Param("userId") Integer userId, @Param("roleId") Integer roleId);
}