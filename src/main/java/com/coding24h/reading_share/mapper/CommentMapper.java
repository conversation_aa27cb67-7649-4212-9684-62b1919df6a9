package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface CommentMapper {
    
    Comment findById(Integer id);
    
    List<Comment> findByNoteId(Integer noteId);
    
    List<Comment> findByUserId(Integer userId);
    
    List<Comment> findByParentId(Integer parentId);
    
    List<Comment> findAll();
    
    void insert(Comment comment);
    
    void update(Comment comment);
    
    void delete(Integer id);
    
    int countByNoteId(Integer noteId);
    
    int countByUserId(Integer userId);
}
