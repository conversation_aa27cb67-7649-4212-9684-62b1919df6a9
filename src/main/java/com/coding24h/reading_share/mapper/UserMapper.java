package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.User;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface UserMapper {
    
    User findById(Integer id);
    
    User findByUsername(String username);
    
    List<User> findAll();
    
    void insert(User user);
    
    void update(User user);
    
    void delete(Integer id);
}