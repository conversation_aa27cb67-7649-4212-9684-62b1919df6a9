package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.Comment;
import com.coding24h.reading_share.service.NoteService;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.service.CommentService;
import com.coding24h.reading_share.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Controller
@RequestMapping("/note")
public class NoteController {

    @Autowired
    private NoteService noteService;
    
    @Autowired
    private CategoryService categoryService;

    @Autowired
    private CommentService commentService;

    @Autowired
    private SecurityUtils securityUtils;
    
    // 显示所有已发布的心得
    @GetMapping("/list")
    public String listNotes(@RequestParam(defaultValue = "0") int page,
                           @RequestParam(defaultValue = "12") int size,
                           Model model) {
        // 分页查询心得
        List<Note> notes = noteService.findAllPublishedWithPaging(page, size);
        int totalNotes = noteService.countAllPublished();
        int totalPages = (int) Math.ceil((double) totalNotes / size);

        List<Category> categories = categoryService.findAll();

        model.addAttribute("notes", notes);
        model.addAttribute("categories", categories);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("pageSize", size);
        model.addAttribute("totalNotes", totalNotes);

        return "note/list";
    }
    
    // 显示心得详情
    @GetMapping("/view/{id}")
    public String viewNote(@PathVariable Integer id, Model model) {
        Note note = noteService.findById(id);
        if (note == null) {
            return "redirect:/note/list";
        }

        // 检查权限：已发布的心得所有人都可以看，未发布的只有作者和管理员可以看
        if (!note.getStatus().equals("published")) {
            User currentUser = securityUtils.getCurrentUser();
            boolean isOwner = currentUser != null && currentUser.getId().equals(note.getUserId());
            boolean isAdmin = currentUser != null &&
                securityUtils.getCurrentUser() != null &&
                currentUser.getRoles().stream().anyMatch(role -> role.getName().equals("ROLE_ADMIN"));

            if (!isOwner && !isAdmin) {
                return "redirect:/note/list";
            }
        }

        // 增加浏览次数
        noteService.incrementViewCount(id);

        // 获取评论列表
        List<Comment> comments = commentService.findByNoteId(id);

        // 获取相关心得
        List<Note> relatedNotes = noteService.findRelated(note.getCategoryId(), id, 5);

        model.addAttribute("note", note);
        model.addAttribute("comments", comments);
        model.addAttribute("relatedNotes", relatedNotes);
        return "note/view";
    }
    
    // 显示创建心得表单
    @GetMapping("/create")
    public String showCreateForm(Model model) {
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        model.addAttribute("note", new Note());
        return "note/form";
    }
    
    // 处理创建心得请求
    @PostMapping("/create")
    public String createNote(@ModelAttribute Note note, @RequestParam("coverFile") MultipartFile coverFile) {
        // 设置当前用户为创建者
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        note.setUserId(currentUser.getId());
        
        // 处理封面图片上传
        if (!coverFile.isEmpty()) {
            String coverImagePath = noteService.uploadCoverImage(coverFile);
            note.setCoverImage(coverImagePath);
        }
        
        noteService.create(note);
        return "redirect:/note/my-notes";
    }
    
    // 显示编辑心得表单
    @GetMapping("/edit/{id}")
    @PreAuthorize("@securityUtils.isNoteOwnerOrAdmin(#id)")
    public String showEditForm(@PathVariable("id") Integer id, Model model) {
        Note note = noteService.findById(id);
        if (note == null) {
            return "redirect:/note/list";
        }
        
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        model.addAttribute("note", note);
        return "note/form";
    }
    
    // 处理编辑心得请求
    @PostMapping("/edit/{id}")
    @PreAuthorize("@securityUtils.isNoteOwnerOrAdmin(#id)")
    public String updateNote(@PathVariable("id") Integer id, @ModelAttribute Note note,
                            @RequestParam("coverFile") MultipartFile coverFile) {
        // 设置心得ID
        note.setId(id);

        // 处理封面图片上传
        if (!coverFile.isEmpty()) {
            String coverImagePath = noteService.uploadCoverImage(coverFile);
            note.setCoverImage(coverImagePath);
        }

        noteService.update(note);
        return "redirect:/note/my-notes";
    }
    
    // 删除心得
    @GetMapping("/delete/{id}")
    @PreAuthorize("@securityUtils.isNoteOwnerOrAdmin(#id)")
    public String deleteNote(@PathVariable("id") Integer id) {
        noteService.delete(id);
        return "redirect:/note/my-notes";
    }
    
    // 显示当前用户的所有心得
    @GetMapping("/my-notes")
    public String myNotes(Model model) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        List<Note> notes = noteService.findByUserId(currentUser.getId());
        model.addAttribute("notes", notes);
        return "note/my-notes";
    }
    
    // 按分类查看心得
    @GetMapping("/category/{categoryId}")
    public String notesByCategory(@PathVariable Integer categoryId,
                                 @RequestParam(defaultValue = "0") int page,
                                 @RequestParam(defaultValue = "12") int size,
                                 Model model) {
        // 分页查询分类下的心得
        List<Note> notes = noteService.findByCategoryIdWithPaging(categoryId, page, size);
        int totalNotes = noteService.countByCategoryIdPublished(categoryId);
        int totalPages = (int) Math.ceil((double) totalNotes / size);

        Category category = categoryService.findById(categoryId);
        List<Category> categories = categoryService.findAll();

        model.addAttribute("notes", notes);
        model.addAttribute("category", category);
        model.addAttribute("categories", categories);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("pageSize", size);
        model.addAttribute("totalNotes", totalNotes);

        return "note/list";
    }
}