package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.Role;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.service.NoteService;
import com.coding24h.reading_share.service.UserService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
public class HomeController {

    @Autowired
    private NoteService noteService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @GetMapping(value = {"", "/", "/index"})
    public String index(Model model) {
        // 获取最新的6篇心得
        List<Note> latestNotes = noteService.findLatest(6);
        model.addAttribute("latestNotes", latestNotes);
        
        // 获取所有分类
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        
        return "index";
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }
    
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "register";
    }
    
    @GetMapping("/error")
    public String error() {
        return "error";
    }

    @GetMapping("/admin-status")
    @ResponseBody
    public String adminStatus() {
        try {
            StringBuilder result = new StringBuilder();
            result.append("<h2>🔍 详细Admin状态检查</h2>");

            // 1. 检查admin用户是否存在
            User admin = userService.findByUsername("admin");
            if (admin == null) {
                result.append("❌ <strong>问题：</strong>admin用户不存在<br>");
                result.append("📝 <strong>解决方案：</strong>执行SQL脚本修复<br>");
                return result.toString();
            }

            result.append("✅ admin用户存在<br>");
            result.append("📋 用户ID: ").append(admin.getId()).append("<br>");
            result.append("📋 用户名: ").append(admin.getUsername()).append("<br>");
            result.append("📋 昵称: ").append(admin.getNickname()).append("<br>");
            result.append("📋 启用状态: ").append(admin.getEnabled() ? "启用" : "禁用").append("<br>");

            // 2. 检查密码
            boolean passwordMatch = passwordEncoder.matches("admin123", admin.getPassword());
            result.append("📋 密码验证: ").append(passwordMatch ? "✅ 正确" : "❌ 错误").append("<br>");
            result.append("📋 存储的密码哈希: ").append(admin.getPassword().substring(0, 20)).append("...<br>");

            // 3. 检查角色
            if (admin.getRoles() == null || admin.getRoles().isEmpty()) {
                result.append("❌ <strong>问题：</strong>admin用户没有角色<br>");
            } else {
                result.append("✅ admin用户角色列表:<br>");
                for (Role role : admin.getRoles()) {
                    result.append("&nbsp;&nbsp;- ").append(role.getName()).append(" (").append(role.getDescription()).append(")<br>");
                }
            }

            result.append("<hr>");
            if (admin.getEnabled() && passwordMatch &&
                admin.getRoles() != null && !admin.getRoles().isEmpty()) {
                result.append("🎉 <strong>诊断结果：</strong>admin用户配置正确<br>");
                result.append("📋 <strong>登录信息：</strong><br>");
                result.append("&nbsp;&nbsp;用户名: admin<br>");
                result.append("&nbsp;&nbsp;密码: admin123<br>");
                result.append("<p><a href='/login'>点击这里去登录</a></p>");
            } else {
                result.append("⚠️ <strong>诊断结果：</strong>admin用户配置有问题<br>");
                result.append("📝 <strong>修复步骤：</strong><br>");
                result.append("1. 执行SQL脚本：<code>mysql -u root -p reading_share < simple_admin_fix.sql</code><br>");
                result.append("2. 重启应用程序<br>");
                result.append("3. 使用 admin/admin123 登录<br>");
            }

            return result.toString();
        } catch (Exception e) {
            return "❌ 错误：" + e.getMessage() + "<br>堆栈跟踪：" + e.getStackTrace()[0];
        }
    }

    @GetMapping("/fix-admin-password")
    @ResponseBody
    public String fixAdminPassword() {
        try {
            User admin = userService.findByUsername("admin");
            if (admin == null) {
                return "❌ admin用户不存在";
            }

            // 重新设置密码
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setEnabled(true);
            userService.update(admin);

            // 验证修复结果
            User updatedAdmin = userService.findByUsername("admin");
            boolean passwordMatch = passwordEncoder.matches("admin123", updatedAdmin.getPassword());

            StringBuilder result = new StringBuilder();
            result.append("<h2>🔧 Admin密码修复结果</h2>");
            result.append("✅ 密码已重置<br>");
            result.append("📋 新密码哈希: ").append(updatedAdmin.getPassword().substring(0, 20)).append("...<br>");
            result.append("📋 密码验证: ").append(passwordMatch ? "✅ 成功" : "❌ 失败").append("<br>");

            if (passwordMatch) {
                result.append("<hr>");
                result.append("🎉 <strong>修复完成！</strong><br>");
                result.append("📋 <strong>登录信息：</strong><br>");
                result.append("&nbsp;&nbsp;用户名: admin<br>");
                result.append("&nbsp;&nbsp;密码: admin123<br>");
                result.append("<p><a href='/login'>点击这里去登录</a></p>");
            }

            return result.toString();
        } catch (Exception e) {
            return "❌ 修复失败：" + e.getMessage();
        }
    }
}