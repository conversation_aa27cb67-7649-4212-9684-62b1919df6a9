package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.service.NoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
public class HomeController {

    @Autowired
    private NoteService noteService;
    
    @Autowired
    private CategoryService categoryService;
    
    @GetMapping(value = {"", "/", "/index"})
    public String index(Model model) {
        // 获取最新的6篇心得
        List<Note> latestNotes = noteService.findLatest(6);
        model.addAttribute("latestNotes", latestNotes);
        
        // 获取所有分类
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        
        return "index";
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }
    
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "register";
    }
    
    @GetMapping("/error")
    public String error() {
        return "error";
    }

    @GetMapping("/admin-status")
    @ResponseBody
    public String adminStatus() {
        try {
            // 简单的admin状态检查
            return "<h2>Admin状态检查</h2>" +
                   "<p>如果您看到这个页面，说明应用程序运行正常。</p>" +
                   "<p>请执行以下步骤：</p>" +
                   "<ol>" +
                   "<li>执行SQL脚本：<code>mysql -u root -p reading_share < simple_admin_fix.sql</code></li>" +
                   "<li>使用 admin/admin123 登录</li>" +
                   "</ol>" +
                   "<p><a href='/login'>点击这里去登录</a></p>";
        } catch (Exception e) {
            return "错误：" + e.getMessage();
        }
    }
}