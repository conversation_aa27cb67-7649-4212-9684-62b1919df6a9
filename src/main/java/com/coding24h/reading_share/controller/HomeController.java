package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.service.NoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
public class HomeController {

    @Autowired
    private NoteService noteService;
    
    @Autowired
    private CategoryService categoryService;
    
    @GetMapping(value = {"", "/", "/index"})
    public String index(Model model) {
        // 获取最新的6篇心得
        List<Note> latestNotes = noteService.findLatest(6);
        model.addAttribute("latestNotes", latestNotes);
        
        // 获取所有分类
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        
        return "index";
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }
    
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "register";
    }
    
    @GetMapping("/error")
    public String error() {
        return "error";
    }
}