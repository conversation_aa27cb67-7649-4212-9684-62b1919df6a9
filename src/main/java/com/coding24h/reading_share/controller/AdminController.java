package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.Note;
import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.Comment;
import com.coding24h.reading_share.service.UserService;
import com.coding24h.reading_share.service.NoteService;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private NoteService noteService;
    
    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private CommentService commentService;

    // 管理员首页
    @GetMapping({"", "/dashboard"})
    public String adminIndex(Model model) {
        // 获取统计信息
        List<User> users = userService.findAll();
        List<Note> notes = noteService.findAll();
        List<Category> categories = categoryService.findAll();
        
        model.addAttribute("userCount", users.size());
        model.addAttribute("noteCount", notes.size());
        model.addAttribute("categoryCount", categories.size());
        
        return "admin/index";
    }

    // 用户管理
    @GetMapping("/users")
    public String manageUsers(Model model) {
        List<User> users = userService.findAll();
        model.addAttribute("users", users);
        return "admin/users";
    }

    // 禁用/启用用户
    @PostMapping("/users/{id}/toggle")
    public String toggleUserStatus(@PathVariable Integer id) {
        User user = userService.findById(id);
        user.setEnabled(!user.getEnabled());
        userService.update(user);
        return "redirect:/admin/users";
    }

    // 删除用户
    @GetMapping("/users/{id}/delete")
    public String deleteUser(@PathVariable Integer id) {
        userService.delete(id);
        return "redirect:/admin/users";
    }

    // 心得管理
    @GetMapping("/notes")
    public String manageNotes(Model model) {
        List<Note> notes = noteService.findAll();
        model.addAttribute("notes", notes);
        return "admin/notes";
    }

    // 删除心得
    @GetMapping("/notes/{id}/delete")
    public String deleteNote(@PathVariable Integer id) {
        noteService.delete(id);
        return "redirect:/admin/notes";
    }

    // 分类管理
    @GetMapping("/categories")
    public String manageCategories(Model model) {
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        return "admin/categories";
    }

    // 评论管理
    @GetMapping("/comments")
    public String manageComments(Model model) {
        // 这里需要实现获取所有评论的方法
        return "admin/comments";
    }

    // 删除评论
    @GetMapping("/comments/{id}/delete")
    public String deleteComment(@PathVariable Integer id) {
        commentService.delete(id);
        return "redirect:/admin/comments";
    }
}
