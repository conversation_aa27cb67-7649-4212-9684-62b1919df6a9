package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Category;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.CategoryService;
import com.coding24h.reading_share.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private SecurityUtils securityUtils;

    // 显示所有分类
    @GetMapping("/list")
    public String listCategories(Model model) {
        List<Category> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        return "category/list";
    }

    // 显示创建分类表单
    @GetMapping("/create")
    @PreAuthorize("hasAnyRole('ADMIN', 'EDITOR')")
    public String showCreateForm(Model model) {
        model.addAttribute("category", new Category());
        return "category/form";
    }

    // 处理创建分类请求
    @PostMapping("/create")
    @PreAuthorize("hasAnyRole('ADMIN', 'EDITOR')")
    public String createCategory(@ModelAttribute Category category) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        category.setCreateUserId(currentUser.getId());
        categoryService.create(category);
        return "redirect:/category/list";
    }

    // 显示编辑分类表单
    @GetMapping("/edit/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'EDITOR')")
    public String showEditForm(@PathVariable Integer id, Model model) {
        Category category = categoryService.findById(id);
        model.addAttribute("category", category);
        return "category/form";
    }

    // 处理编辑分类请求
    @PostMapping("/edit/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'EDITOR')")
    public String updateCategory(@PathVariable Integer id, @ModelAttribute Category category) {
        category.setId(id);
        categoryService.update(category);
        return "redirect:/category/list";
    }

    // 删除分类
    @GetMapping("/delete/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteCategory(@PathVariable Integer id) {
        categoryService.delete(id);
        return "redirect:/category/list";
    }
}
