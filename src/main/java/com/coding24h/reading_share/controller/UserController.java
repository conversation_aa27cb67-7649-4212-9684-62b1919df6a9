package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.UserService;
import com.coding24h.reading_share.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private SecurityUtils securityUtils;

    // 用户注册处理
    @PostMapping("/register")
    public String register(@ModelAttribute User user, Model model) {
        try {
            // 检查用户名是否已存在
            if (userService.findByUsername(user.getUsername()) != null) {
                model.addAttribute("error", "用户名已存在");
                return "register";
            }
            
            userService.register(user);
            return "redirect:/login?registered";
        } catch (Exception e) {
            model.addAttribute("error", "注册失败：" + e.getMessage());
            return "register";
        }
    }

    // 显示用户个人资料
    @GetMapping("/profile")
    public String showProfile(Model model) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser != null) {
            User user = userService.findByUsername(currentUser.getUsername());
            model.addAttribute("user", user);
        }
        return "user/profile";
    }

    // 显示编辑个人资料表单
    @GetMapping("/edit-profile")
    public String showEditProfileForm(Model model) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser != null) {
            User user = userService.findByUsername(currentUser.getUsername());
            model.addAttribute("user", user);
        }
        return "user/edit-profile";
    }

    // 处理编辑个人资料请求
    @PostMapping("/edit-profile")
    public String updateProfile(@ModelAttribute User user) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser != null) {
            user.setId(currentUser.getId());
            user.setUsername(currentUser.getUsername()); // 不允许修改用户名
            userService.update(user);
        }
        return "redirect:/user/profile";
    }
}
