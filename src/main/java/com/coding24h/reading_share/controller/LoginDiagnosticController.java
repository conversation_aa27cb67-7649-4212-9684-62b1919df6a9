package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.Role;
import com.coding24h.reading_share.mapper.UserMapper;
import com.coding24h.reading_share.mapper.RoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/login-diagnostic")
public class LoginDiagnosticController {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/check-admin")
    @ResponseBody
    public String checkAdminUser() {
        StringBuilder result = new StringBuilder();
        result.append("<h2>管理员登录诊断报告</h2>");
        
        try {
            // 1. 检查admin用户是否存在
            User admin = userMapper.findByUsername("admin");
            if (admin == null) {
                result.append("❌ <strong>问题发现：</strong>admin用户不存在<br>");
                result.append("📝 <strong>解决方案：</strong>执行 admin_login_fix.sql 脚本<br><br>");
                return result.toString();
            }
            
            result.append("✅ admin用户存在<br>");
            result.append("📋 用户ID: ").append(admin.getId()).append("<br>");
            result.append("📋 用户名: ").append(admin.getUsername()).append("<br>");
            result.append("📋 昵称: ").append(admin.getNickname()).append("<br>");
            result.append("📋 启用状态: ").append(admin.getEnabled() ? "启用" : "禁用").append("<br>");
            
            // 2. 检查用户是否启用
            if (!admin.getEnabled()) {
                result.append("❌ <strong>问题发现：</strong>admin用户被禁用<br>");
                result.append("📝 <strong>解决方案：</strong>执行 admin_login_fix.sql 脚本<br><br>");
            }
            
            // 3. 检查密码
            boolean passwordMatch = passwordEncoder.matches("admin123", admin.getPassword());
            result.append("📋 密码验证: ").append(passwordMatch ? "✅ 正确" : "❌ 错误").append("<br>");
            if (!passwordMatch) {
                result.append("❌ <strong>问题发现：</strong>admin密码不正确<br>");
                result.append("📝 <strong>解决方案：</strong>执行 admin_login_fix.sql 脚本<br><br>");
            }
            
            // 4. 检查角色
            if (admin.getRoles() == null || admin.getRoles().isEmpty()) {
                result.append("❌ <strong>问题发现：</strong>admin用户没有分配角色<br>");
                result.append("📝 <strong>解决方案：</strong>执行 admin_login_fix.sql 脚本<br><br>");
            } else {
                result.append("✅ admin用户已分配角色:<br>");
                for (Role role : admin.getRoles()) {
                    result.append("&nbsp;&nbsp;- ").append(role.getName()).append(" (").append(role.getDescription()).append(")<br>");
                }
                
                // 检查是否有ADMIN角色
                boolean hasAdminRole = admin.getRoles().stream()
                    .anyMatch(role -> "ROLE_ADMIN".equals(role.getName()));
                if (!hasAdminRole) {
                    result.append("❌ <strong>问题发现：</strong>admin用户没有ROLE_ADMIN角色<br>");
                    result.append("📝 <strong>解决方案：</strong>执行 admin_login_fix.sql 脚本<br><br>");
                }
            }
            
            // 5. 总结
            result.append("<hr>");
            if (admin.getEnabled() && passwordMatch && 
                admin.getRoles() != null && !admin.getRoles().isEmpty() &&
                admin.getRoles().stream().anyMatch(role -> "ROLE_ADMIN".equals(role.getName()))) {
                result.append("🎉 <strong>诊断结果：</strong>admin用户配置正确，应该可以正常登录<br>");
                result.append("📋 <strong>登录信息：</strong><br>");
                result.append("&nbsp;&nbsp;用户名: admin<br>");
                result.append("&nbsp;&nbsp;密码: admin123<br>");
            } else {
                result.append("⚠️ <strong>诊断结果：</strong>admin用户配置有问题，需要修复<br>");
                result.append("📝 <strong>修复步骤：</strong><br>");
                result.append("1. 在MySQL中执行 admin_login_fix.sql 脚本<br>");
                result.append("2. 重启应用程序<br>");
                result.append("3. 使用 admin/admin123 登录<br>");
            }
            
        } catch (Exception e) {
            result.append("❌ <strong>检查过程中发生错误：</strong>").append(e.getMessage()).append("<br>");
            result.append("📝 <strong>可能原因：</strong>数据库连接问题或表结构不正确<br>");
        }
        
        return result.toString();
    }
    
    @GetMapping("/fix-admin")
    @ResponseBody
    public String fixAdminUser() {
        StringBuilder result = new StringBuilder();
        result.append("<h2>管理员用户修复</h2>");
        
        try {
            // 1. 确保ROLE_ADMIN角色存在
            Role adminRole = roleMapper.findByName("ROLE_ADMIN");
            if (adminRole == null) {
                adminRole = new Role();
                adminRole.setName("ROLE_ADMIN");
                adminRole.setDescription("管理员");
                roleMapper.insert(adminRole);
                result.append("✅ 创建ROLE_ADMIN角色<br>");
            }
            
            // 2. 创建或更新admin用户
            User admin = userMapper.findByUsername("admin");
            if (admin == null) {
                // 创建新用户
                admin = new User();
                admin.setUsername("admin");
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setNickname("系统管理员");
                admin.setEnabled(true);
                userMapper.insert(admin);
                result.append("✅ 创建admin用户<br>");
            } else {
                // 更新现有用户
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setEnabled(true);
                userMapper.update(admin);
                result.append("✅ 更新admin用户<br>");
            }
            
            // 3. 分配管理员角色
            roleMapper.assignRoleToUser(admin.getId(), adminRole.getId());
            result.append("✅ 分配管理员角色<br>");
            
            result.append("<hr>");
            result.append("🎉 <strong>修复完成！</strong><br>");
            result.append("📋 <strong>登录信息：</strong><br>");
            result.append("&nbsp;&nbsp;用户名: admin<br>");
            result.append("&nbsp;&nbsp;密码: admin123<br>");
            result.append("<br><a href='/login'>点击这里去登录</a>");
            
        } catch (Exception e) {
            result.append("❌ <strong>修复过程中发生错误：</strong>").append(e.getMessage()).append("<br>");
        }
        
        return result.toString();
    }
}
