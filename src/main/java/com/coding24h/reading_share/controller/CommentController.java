package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Comment;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.CommentService;
import com.coding24h.reading_share.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/comment")
public class CommentController {

    @Autowired
    private CommentService commentService;

    @Autowired
    private SecurityUtils securityUtils;

    // 创建评论
    @PostMapping("/create")
    public String createComment(@ModelAttribute Comment comment,
                               @RequestParam("noteId") Integer noteId) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        comment.setUserId(currentUser.getId());
        comment.setNoteId(noteId);
        
        commentService.create(comment);
        return "redirect:/note/view/" + noteId;
    }

    // 回复评论
    @PostMapping("/reply")
    public String replyComment(@ModelAttribute Comment comment,
                              @RequestParam("noteId") Integer noteId,
                              @RequestParam("parentId") Integer parentId) {
        User currentUser = securityUtils.getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        comment.setUserId(currentUser.getId());
        comment.setNoteId(noteId);
        comment.setParentId(parentId);
        
        commentService.create(comment);
        return "redirect:/note/view/" + noteId;
    }

    // 删除评论
    @GetMapping("/delete/{id}")
    @PreAuthorize("@securityUtils.isCommentOwnerOrAdmin(#id)")
    public String deleteComment(@PathVariable("id") Integer id,
                               @RequestParam("noteId") Integer noteId) {
        commentService.delete(id);
        return "redirect:/note/view/" + noteId;
    }

    // 编辑评论
    @PostMapping("/edit/{id}")
    @PreAuthorize("@securityUtils.isCommentOwnerOrAdmin(#id)")
    public String updateComment(@PathVariable("id") Integer id,
                               @ModelAttribute Comment comment,
                               @RequestParam("noteId") Integer noteId) {
        comment.setId(id);
        commentService.update(comment);
        return "redirect:/note/view/" + noteId;
    }
}
