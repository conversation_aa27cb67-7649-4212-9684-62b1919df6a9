/* 主样式文件 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    /* 启用硬件加速 */
    transform: translateZ(0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    /* 启用硬件加速 */
    will-change: transform, box-shadow;
    backface-visibility: hidden;
}

.card:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    /* 优化图片加载 */
    loading: lazy;
    transition: opacity 0.3s ease;
}

/* 懒加载图片样式 */
img.lazy {
    opacity: 0;
}

img.lazy.loaded {
    opacity: 1;
}

.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

footer {
    margin-top: auto;
}

.content-wrapper {
    min-height: calc(100vh - 200px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .jumbotron p {
        font-size: 1rem;
    }
}

/* 表单样式 */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 评论样式 */
.comment {
    border-left: 3px solid #667eea;
    padding-left: 15px;
    margin-bottom: 15px;
}

.comment-reply {
    margin-left: 30px;
    border-left: 2px solid #ddd;
}

/* 心得详情页样式 */
.note-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.note-meta {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* 管理后台样式 */
.admin-sidebar {
    background-color: #343a40;
    min-height: calc(100vh - 56px);
}

.admin-sidebar .nav-link {
    color: #adb5bd;
}

.admin-sidebar .nav-link:hover {
    color: #fff;
}

.admin-sidebar .nav-link.active {
    color: #fff;
    background-color: #495057;
}
