// 主JavaScript文件

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示（延迟加载）
    setTimeout(function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }, 100);

    // 自动隐藏警告消息
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);

    // 图片懒加载
    initLazyLoading();

    // 确认删除操作
    var deleteLinks = document.querySelectorAll('a[href*="/delete/"]');
    deleteLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            if (!confirm('确定要删除吗？此操作不可恢复。')) {
                e.preventDefault();
            }
        });
    });

    // 表单验证
    var forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});

// 图片懒加载功能
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理：直接加载所有图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// 图片预览功能
function previewImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            var preview = document.getElementById('imagePreview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// 字符计数功能
function updateCharCount(textarea, counterId) {
    var counter = document.getElementById(counterId);
    if (counter) {
        var current = textarea.value.length;
        var max = textarea.getAttribute('maxlength') || 1000;
        counter.textContent = current + '/' + max;
        
        if (current > max * 0.9) {
            counter.classList.add('text-warning');
        } else {
            counter.classList.remove('text-warning');
        }
    }
}

// 回复评论功能
function toggleReplyForm(commentId) {
    var replyForm = document.getElementById('replyForm' + commentId);
    if (replyForm) {
        if (replyForm.style.display === 'none' || replyForm.style.display === '') {
            replyForm.style.display = 'block';
        } else {
            replyForm.style.display = 'none';
        }
    }
}

// 搜索功能
function performSearch() {
    var searchTerm = document.getElementById('searchInput').value;
    if (searchTerm.trim()) {
        window.location.href = '/search?q=' + encodeURIComponent(searchTerm);
    }
}

// 收藏功能
function toggleFavorite(noteId) {
    fetch('/favorite/toggle/' + noteId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        var favoriteBtn = document.getElementById('favoriteBtn');
        if (data.favorited) {
            favoriteBtn.classList.remove('btn-outline-warning');
            favoriteBtn.classList.add('btn-warning');
            favoriteBtn.innerHTML = '<i class="fas fa-star"></i> 已收藏';
        } else {
            favoriteBtn.classList.remove('btn-warning');
            favoriteBtn.classList.add('btn-outline-warning');
            favoriteBtn.innerHTML = '<i class="far fa-star"></i> 收藏';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败，请重试');
    });
}
