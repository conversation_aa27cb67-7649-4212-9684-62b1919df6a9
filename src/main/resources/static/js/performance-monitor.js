// 前端性能监控工具
// 用于监控和分析页面性能

(function() {
    'use strict';

    // 性能监控配置
    const config = {
        enableLogging: true,
        enableConsoleOutput: true,
        enableLocalStorage: true,
        maxLogEntries: 100
    };

    // 性能数据收集器
    const PerformanceMonitor = {
        
        // 初始化监控
        init: function() {
            if (!window.performance || !window.performance.timing) {
                console.warn('Performance API not supported');
                return;
            }

            // 页面加载完成后收集数据
            if (document.readyState === 'complete') {
                this.collectMetrics();
            } else {
                window.addEventListener('load', () => {
                    setTimeout(() => this.collectMetrics(), 100);
                });
            }

            // 监控资源加载
            this.monitorResources();
            
            // 监控用户交互
            this.monitorInteractions();
        },

        // 收集性能指标
        collectMetrics: function() {
            const timing = performance.timing;
            const navigation = performance.navigation;
            
            const metrics = {
                // 页面加载时间
                pageLoadTime: timing.loadEventEnd - timing.navigationStart,
                
                // DNS查询时间
                dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
                
                // TCP连接时间
                tcpTime: timing.connectEnd - timing.connectStart,
                
                // 请求响应时间
                requestTime: timing.responseEnd - timing.requestStart,
                
                // DOM解析时间
                domParseTime: timing.domContentLoadedEventEnd - timing.domLoading,
                
                // 资源加载时间
                resourceLoadTime: timing.loadEventEnd - timing.domContentLoadedEventEnd,
                
                // 首次渲染时间
                firstPaintTime: this.getFirstPaintTime(),
                
                // 导航类型
                navigationType: navigation.type,
                
                // 重定向次数
                redirectCount: navigation.redirectCount,
                
                // 时间戳
                timestamp: new Date().toISOString(),
                
                // 页面URL
                url: window.location.href
            };

            this.logMetrics(metrics);
            this.analyzePerformance(metrics);
        },

        // 获取首次渲染时间
        getFirstPaintTime: function() {
            if (window.performance && window.performance.getEntriesByType) {
                const paintEntries = performance.getEntriesByType('paint');
                const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
                return firstPaint ? firstPaint.startTime : null;
            }
            return null;
        },

        // 监控资源加载
        monitorResources: function() {
            if (!window.performance || !window.performance.getEntriesByType) return;

            const resources = performance.getEntriesByType('resource');
            const slowResources = resources.filter(resource => resource.duration > 1000);
            
            if (slowResources.length > 0 && config.enableLogging) {
                console.group('🐌 慢加载资源 (>1s)');
                slowResources.forEach(resource => {
                    console.log(`${resource.name}: ${Math.round(resource.duration)}ms`);
                });
                console.groupEnd();
            }

            // 统计资源类型
            const resourceStats = this.analyzeResourceTypes(resources);
            this.logResourceStats(resourceStats);
        },

        // 分析资源类型
        analyzeResourceTypes: function(resources) {
            const stats = {
                css: { count: 0, totalSize: 0, totalTime: 0 },
                js: { count: 0, totalSize: 0, totalTime: 0 },
                img: { count: 0, totalSize: 0, totalTime: 0 },
                other: { count: 0, totalSize: 0, totalTime: 0 }
            };

            resources.forEach(resource => {
                const type = this.getResourceType(resource.name);
                stats[type].count++;
                stats[type].totalSize += resource.transferSize || 0;
                stats[type].totalTime += resource.duration || 0;
            });

            return stats;
        },

        // 获取资源类型
        getResourceType: function(url) {
            if (url.match(/\.(css)$/i)) return 'css';
            if (url.match(/\.(js)$/i)) return 'js';
            if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'img';
            return 'other';
        },

        // 监控用户交互
        monitorInteractions: function() {
            let clickCount = 0;
            let scrollCount = 0;

            document.addEventListener('click', () => {
                clickCount++;
            });

            let scrollTimer;
            window.addEventListener('scroll', () => {
                clearTimeout(scrollTimer);
                scrollTimer = setTimeout(() => {
                    scrollCount++;
                }, 100);
            });

            // 5分钟后记录交互数据
            setTimeout(() => {
                this.logInteractionStats({
                    clicks: clickCount,
                    scrolls: scrollCount,
                    timeOnPage: 300 // 5分钟
                });
            }, 300000);
        },

        // 记录性能指标
        logMetrics: function(metrics) {
            if (config.enableConsoleOutput) {
                console.group('📊 页面性能指标');
                console.log(`页面加载时间: ${metrics.pageLoadTime}ms`);
                console.log(`DNS查询时间: ${metrics.dnsTime}ms`);
                console.log(`TCP连接时间: ${metrics.tcpTime}ms`);
                console.log(`请求响应时间: ${metrics.requestTime}ms`);
                console.log(`DOM解析时间: ${metrics.domParseTime}ms`);
                console.log(`资源加载时间: ${metrics.resourceLoadTime}ms`);
                if (metrics.firstPaintTime) {
                    console.log(`首次渲染时间: ${Math.round(metrics.firstPaintTime)}ms`);
                }
                console.groupEnd();
            }

            if (config.enableLocalStorage) {
                this.saveToLocalStorage('performance_metrics', metrics);
            }
        },

        // 记录资源统计
        logResourceStats: function(stats) {
            if (config.enableConsoleOutput) {
                console.group('📦 资源加载统计');
                Object.keys(stats).forEach(type => {
                    const stat = stats[type];
                    if (stat.count > 0) {
                        console.log(`${type.toUpperCase()}: ${stat.count}个文件, ${Math.round(stat.totalTime)}ms, ${Math.round(stat.totalSize/1024)}KB`);
                    }
                });
                console.groupEnd();
            }
        },

        // 记录交互统计
        logInteractionStats: function(stats) {
            if (config.enableConsoleOutput) {
                console.group('👆 用户交互统计');
                console.log(`点击次数: ${stats.clicks}`);
                console.log(`滚动次数: ${stats.scrolls}`);
                console.log(`页面停留时间: ${stats.timeOnPage}秒`);
                console.groupEnd();
            }

            if (config.enableLocalStorage) {
                this.saveToLocalStorage('interaction_stats', stats);
            }
        },

        // 性能分析和建议
        analyzePerformance: function(metrics) {
            const suggestions = [];

            if (metrics.pageLoadTime > 3000) {
                suggestions.push('⚠️ 页面加载时间过长，建议优化资源加载');
            }

            if (metrics.dnsTime > 200) {
                suggestions.push('⚠️ DNS查询时间较长，建议使用DNS预解析');
            }

            if (metrics.requestTime > 1000) {
                suggestions.push('⚠️ 服务器响应时间较长，建议优化后端性能');
            }

            if (metrics.domParseTime > 1000) {
                suggestions.push('⚠️ DOM解析时间较长，建议减少DOM复杂度');
            }

            if (suggestions.length > 0 && config.enableConsoleOutput) {
                console.group('💡 性能优化建议');
                suggestions.forEach(suggestion => console.log(suggestion));
                console.groupEnd();
            }
        },

        // 保存到本地存储
        saveToLocalStorage: function(key, data) {
            try {
                let logs = JSON.parse(localStorage.getItem(key) || '[]');
                logs.push(data);
                
                // 限制日志条数
                if (logs.length > config.maxLogEntries) {
                    logs = logs.slice(-config.maxLogEntries);
                }
                
                localStorage.setItem(key, JSON.stringify(logs));
            } catch (e) {
                console.warn('无法保存性能数据到本地存储:', e);
            }
        },

        // 获取性能报告
        getPerformanceReport: function() {
            try {
                const metrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
                const interactions = JSON.parse(localStorage.getItem('interaction_stats') || '[]');
                
                return {
                    metrics: metrics,
                    interactions: interactions,
                    summary: this.generateSummary(metrics)
                };
            } catch (e) {
                console.warn('无法获取性能报告:', e);
                return null;
            }
        },

        // 生成性能摘要
        generateSummary: function(metrics) {
            if (metrics.length === 0) return null;

            const avgLoadTime = metrics.reduce((sum, m) => sum + m.pageLoadTime, 0) / metrics.length;
            const maxLoadTime = Math.max(...metrics.map(m => m.pageLoadTime));
            const minLoadTime = Math.min(...metrics.map(m => m.pageLoadTime));

            return {
                averageLoadTime: Math.round(avgLoadTime),
                maxLoadTime: maxLoadTime,
                minLoadTime: minLoadTime,
                totalSessions: metrics.length
            };
        }
    };

    // 导出到全局
    window.PerformanceMonitor = PerformanceMonitor;

    // 自动初始化（仅在开发环境）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        PerformanceMonitor.init();
    }

})();
