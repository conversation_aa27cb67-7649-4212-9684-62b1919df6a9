<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${note.title} + ' - 阅读心得分享平台'">心得详情</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" th:href="@{/}">阅读心得分享</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/}">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/note/list}">心得列表</a>
                </li>
                <li class="nav-item" sec:authorize="isAuthenticated()">
                    <a class="nav-link" th:href="@{/note/create}">发布心得</a>
                </li>
            </ul>
            <div class="d-flex">
                <div sec:authorize="!isAuthenticated()">
                    <a th:href="@{/login}" class="btn btn-outline-light me-2">登录</a>
                    <a th:href="@{/register}" class="btn btn-light">注册</a>
                </div>
                <div sec:authorize="isAuthenticated()" class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span sec:authentication="name">用户名</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" th:href="@{/user/profile}">个人资料</a></li>
                        <li><a class="dropdown-item" th:href="@{/note/my-notes}">我的心得</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form th:action="@{/logout}" method="post">
                                <button class="dropdown-item" type="submit">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <div class="row">
            <!-- 心得内容 -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-body">
                        <h1 class="card-title" th:text="${note.title}">心得标题</h1>
                        <div class="d-flex justify-content-between mb-3">
                            <div>
                                <span class="badge bg-primary" th:text="${note.category.name}">分类</span>
                                <span class="text-muted ms-2">
                                    <i class="bi bi-eye"></i> <span th:text="${note.viewCount}">0</span> 次浏览
                                </span>
                            </div>
                            <div class="text-muted">
                                发布于 <span th:text="${#temporals.format(note.createTime, 'yyyy-MM-dd HH:mm')}">发布时间</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5>《<span th:text="${note.bookName}">书名</span>》</h5>
                            <p class="text-muted">作者: <span th:text="${note.bookAuthor}">作者</span></p>
                        </div>
                        
                        <div class="text-center mb-4" th:if="${note.coverImage}">
                            <img th:src="${note.coverImage}" class="img-fluid rounded" style="max-height: 300px;" alt="封面图">
                        </div>
                        
                        <div class="note-content">
                            <p th:utext="${note.content}">心得内容...</p>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="mt-4" sec:authorize="isAuthenticated()" 
                             th:if="${#authentication.principal.id == note.userId || #authorization.expression('hasRole(''ADMIN'')')}">
                            <a th:href="@{/note/edit/{id}(id=${note.id})}" class="btn btn-outline-primary">编辑</a>
                            <a th:href="@{/note/delete/{id}(id=${note.id})}" class="btn btn-outline-danger" 
                               onclick="return confirm('确定要删除这篇心得吗？')">删除</a>
                        </div>
                    </div>
                </div>
                
                <!-- 评论区 -->
                <div class="card">
                    <div class="card-header">
                        <h4>评论 (<span th:text="${#lists.size(comments)}">0</span>)</h4>
                    </div>
                    <div class="card-body">
                        <!-- 发表评论 -->
                        <div sec:authorize="isAuthenticated()" class="mb-4">
                            <form th:action="@{/comment/create}" method="post">
                                <input type="hidden" name="noteId" th:value="${note.id}">
                                <div class="mb-3">
                                    <textarea name="content" class="form-control" rows="3" placeholder="发表您的评论..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">提交评论</button>
                            </form>
                        </div>
                        
                        <div sec:authorize="!isAuthenticated()" class="alert alert-info">
                            请 <a th:href="@{/login}">登录</a> 后发表评论
                        </div>
                        
                        <!-- 评论列表 -->
                        <div th:if="${#lists.isEmpty(comments)}" class="text-center py-4 text-muted">
                            暂无评论，快来发表第一条评论吧！
                        </div>
                        
                        <div th:each="comment : ${comments}" class="mb-3 border-bottom pb-3">
                            <div class="d-flex">
                                <div>
                                    <img th:if="${comment.user.avatar}" th:src="${comment.user.avatar}" class="rounded-circle" width="40" alt="头像">
                                    <img th:unless="${comment.user.avatar}" th:src="@{/images/default-avatar.png}" class="rounded-circle" width="40" alt="默认头像">
                                </div>
                                <div class="ms-3 flex-grow-1">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-0" th:text="${comment.user.nickname ?: comment.user.username}">用户名</h6>
                                        <small class="text-muted" th:text="${#temporals.format(comment.createTime, 'yyyy-MM-dd HH:mm')}">评论时间</small>
                                    </div>
                                    <p class="mt-2" th:text="${comment.content}">评论内容</p>
                                    
                                    <!-- 删除评论按钮 -->
                                    <div th:if="${#authentication.principal != null && (#authentication.principal.id == comment.userId || #authorization.expression('hasAnyRole(''ADMIN'', ''EDITOR'')'))}">
                                        <a th:href="@{/comment/delete/{id}(id=${comment.id}, noteId=${note.id})}" 
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('确定要删除这条评论吗？')">删除</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏 -->
            <div class="col-md-4">
                <!-- 作者信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>作者信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <img th:if="${note.user.avatar}" th:src="${note.user.avatar}" class="rounded-circle me-3" width="60" alt="头像">
                            <img th:unless="${note.user.avatar}" th:src="@{/images/default-avatar.png}" class="rounded-circle me-3" width="60" alt="默认头像">
                            <div>
                                <h5 th:text="${note.user.nickname ?: note.user.username}">用户名</h5>
                                <a th:href="@{/note/user/{id}(id=${note.userId})}" class="btn btn-sm btn-outline-primary">查看TA的心得</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 收藏按钮 -->
                <div class="card mb-4" sec:authorize="isAuthenticated()">
                    <div class="card-body text-center">
                        <form th:if="${!isFavorited}" th:action="@{/favorite/add}" method="post">
                            <input type="hidden" name="noteId" th:value="${note.id}">
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="bi bi-heart"></i> 收藏此心得
                            </button>
                        </form>
                        <form th:if="${isFavorited}" th:action="@{/favorite/remove}" method="post">
                            <input type="hidden" name="noteId" th:value="${note.id}">
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-heart-fill"></i> 已收藏
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- 相关心得 -->
                <div class="card">
                    <div class="card-header">
                        <h5>相关心得</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(relatedNotes)}" class="text-center py-2 text-muted">
                            暂无相关心得
                        </div>
                        <div th:each="relatedNote : ${relatedNotes}" class="mb-3">
                            <a th:href="@{/note/view/{id}(id=${relatedNote.id})}" class="text-decoration-none">
                                <h6 th:text="${relatedNote.title}">相关心得标题</h6>
                            </a>
                            <p class="text-muted small">
                                <span th:text="${relatedNote.bookName}">书名</span> | 
                                <span th:text="${#temporals.format(relatedNote.createTime, 'yyyy-MM-dd')}">日期</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- 页脚 -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>阅读心得分享平台</h5>
                <p>分享阅读，传递智慧</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>&copy; 2023 阅读心得分享平台 版权所有</p>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/main.js}"></script>
</body>
</html>