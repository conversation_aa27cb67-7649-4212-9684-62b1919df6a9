<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心得列表 - 阅读心得分享平台</title>

    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//cdn.bootcdn.net">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" as="style">
    <link rel="preload" th:href="@{/css/main.css}" as="style">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" th:href="@{/}">阅读心得分享</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/}">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/note/list}">心得列表</a>
                </li>
                <li class="nav-item" sec:authorize="isAuthenticated()">
                    <a class="nav-link" th:href="@{/note/create}">发布心得</a>
                </li>
            </ul>
            <div class="d-flex">
                <div sec:authorize="!isAuthenticated()">
                    <a th:href="@{/login}" class="btn btn-outline-light me-2">登录</a>
                    <a th:href="@{/register}" class="btn btn-light">注册</a>
                </div>
                <div sec:authorize="isAuthenticated()" class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span sec:authentication="name">用户名</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" th:href="@{/user/profile}">个人资料</a></li>
                        <li><a class="dropdown-item" th:href="@{/note/my-notes}">我的心得</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form th:action="@{/logout}" method="post">
                                <button class="dropdown-item" type="submit">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<div class="container mt-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 th:if="${category}" th:text="'分类：' + ${category.name}">心得列表</h2>
                <h2 th:unless="${category}">所有心得</h2>
                <div>
                    <a sec:authorize="isAuthenticated()" th:href="@{/note/create}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 发布心得
                    </a>
                </div>
            </div>

            <!-- 心得列表 -->
            <div th:if="${notes.empty}" class="text-center py-5">
                <h4 class="text-muted">暂无心得</h4>
                <p class="text-muted">成为第一个分享心得的人吧！</p>
                <a sec:authorize="isAuthenticated()" th:href="@{/note/create}" class="btn btn-primary">发布心得</a>
            </div>

            <div class="row row-cols-1 row-cols-md-2 g-4" th:unless="${notes.empty}">
                <div class="col" th:each="note : ${notes}">
                    <div class="card h-100">
                        <img th:if="${note.coverImage}" th:src="${note.coverImage}" class="card-img-top" alt="封面图">
                        <img th:unless="${note.coverImage}" th:src="@{/images/default-cover.jpg}" class="card-img-top" alt="默认封面">
                        <div class="card-body">
                            <h5 class="card-title" th:text="${note.title}">心得标题</h5>
                            <p class="card-text text-muted">
                                <small>
                                    <span th:text="${note.bookName}">书名</span> -
                                    <span th:text="${note.bookAuthor}">作者</span>
                                </small>
                            </p>
                            <p class="card-text" th:text="${#strings.abbreviate(note.content, 150)}">心得内容摘要...</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <span th:text="${note.user.nickname ?: note.user.username}">作者</span>
                                </small>
                                <small class="text-muted">
                                    <span th:text="${#temporals.format(note.createTime, 'MM-dd')}">日期</span>
                                </small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-eye"></i> <span th:text="${note.viewCount}">0</span> 次浏览
                                </small>
                                <a th:href="@{/note/view/{id}(id=${note.id})}" class="btn btn-sm btn-outline-primary">阅读全文</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <nav aria-label="分页导航" class="mt-4" th:if="${totalPages > 1}">
                <ul class="pagination justify-content-center">
                    <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                        <a class="page-link"
                           th:href="${category != null} ? @{/note/category/{id}(id=${category.id}, page=${currentPage - 1})} : @{/note/list(page=${currentPage - 1})}">
                           上一页
                        </a>
                    </li>

                    <!-- 显示页码，最多显示10页 -->
                    <th:block th:with="startPage=${currentPage > 4 ? currentPage - 4 : 0},
                                       endPage=${currentPage + 5 < totalPages ? currentPage + 5 : totalPages - 1}">
                        <li class="page-item" th:if="${startPage > 0}">
                            <a class="page-link"
                               th:href="${category != null} ? @{/note/category/{id}(id=${category.id}, page=0)} : @{/note/list(page=0)}">
                               1
                            </a>
                        </li>
                        <li class="page-item disabled" th:if="${startPage > 1}">
                            <span class="page-link">...</span>
                        </li>

                        <li class="page-item" th:each="i : ${#numbers.sequence(startPage, endPage)}"
                            th:classappend="${i == currentPage} ? 'active'">
                            <a class="page-link"
                               th:href="${category != null} ? @{/note/category/{id}(id=${category.id}, page=${i})} : @{/note/list(page=${i})}"
                               th:text="${i + 1}">1</a>
                        </li>

                        <li class="page-item disabled" th:if="${endPage < totalPages - 2}">
                            <span class="page-link">...</span>
                        </li>
                        <li class="page-item" th:if="${endPage < totalPages - 1}">
                            <a class="page-link"
                               th:href="${category != null} ? @{/note/category/{id}(id=${category.id}, page=${totalPages - 1})} : @{/note/list(page=${totalPages - 1})}"
                               th:text="${totalPages}">最后页</a>
                        </li>
                    </th:block>

                    <li class="page-item" th:classappend="${currentPage == totalPages - 1} ? 'disabled'">
                        <a class="page-link"
                           th:href="${category != null} ? @{/note/category/{id}(id=${category.id}, page=${currentPage + 1})} : @{/note/list(page=${currentPage + 1})}">
                           下一页
                        </a>
                    </li>
                </ul>

                <!-- 分页信息 -->
                <div class="text-center mt-2">
                    <small class="text-muted">
                        第 <span th:text="${currentPage + 1}">1</span> 页，共 <span th:text="${totalPages}">1</span> 页，
                        总计 <span th:text="${totalNotes}">0</span> 篇心得
                    </small>
                </div>
            </nav>
        </div>

        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5>分类</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a th:href="@{/note/list}" class="list-group-item list-group-item-action"
                           th:classappend="${category == null} ? 'active'">
                            全部分类
                        </a>
                        <a th:each="cat : ${categories}" 
                           th:href="@{/note/category/{id}(id=${cat.id})}" 
                           class="list-group-item list-group-item-action"
                           th:classappend="${category != null && category.id == cat.id} ? 'active'"
                           th:text="${cat.name}">分类名称</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>阅读心得分享平台</h5>
                <p>分享阅读，传递智慧</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>&copy; 2023 阅读心得分享平台 版权所有</p>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
<script th:src="@{/js/main.js}"></script>
</body>
</html>
