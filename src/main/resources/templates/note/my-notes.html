<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的心得 - 阅读心得分享平台</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" th:href="@{/}">阅读心得分享</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/}">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/note/list}">心得列表</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/note/create}">发布心得</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/note/my-notes}">我的心得</a>
                </li>
            </ul>
            <div class="d-flex">
                <div class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span sec:authentication="name">用户名</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" th:href="@{/user/profile}">个人资料</a></li>
                        <li><a class="dropdown-item" th:href="@{/note/my-notes}">我的心得</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form th:action="@{/logout}" method="post">
                                <button class="dropdown-item" type="submit">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>我的心得</h2>
        <a th:href="@{/note/create}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 发布新心得
        </a>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title" th:text="${#lists.size(notes)}">0</h5>
                    <p class="card-text">总心得数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title" th:text="${#lists.size(notes.?[status == 'published'])}">0</h5>
                    <p class="card-text">已发布</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title" th:text="${#lists.size(notes.?[status == 'draft'])}">0</h5>
                    <p class="card-text">草稿</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title" th:text="${#aggregates.sum(notes.![viewCount])}">0</h5>
                    <p class="card-text">总浏览量</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 心得列表 -->
    <div th:if="${notes.empty}" class="text-center py-5">
        <h4 class="text-muted">您还没有发布任何心得</h4>
        <p class="text-muted">快来分享您的第一篇读书心得吧！</p>
        <a th:href="@{/note/create}" class="btn btn-primary">发布心得</a>
    </div>

    <div class="row" th:unless="${notes.empty}">
        <div class="col-12">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>标题</th>
                            <th>书籍</th>
                            <th>分类</th>
                            <th>状态</th>
                            <th>浏览量</th>
                            <th>发布时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="note : ${notes}">
                            <td>
                                <a th:href="@{/note/view/{id}(id=${note.id})}" 
                                   th:text="${note.title}" class="text-decoration-none">心得标题</a>
                            </td>
                            <td>
                                <div>
                                    <strong th:text="${note.bookName}">书名</strong>
                                    <br>
                                    <small class="text-muted" th:text="${note.bookAuthor}">作者</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary" th:text="${note.category?.name}">分类</span>
                            </td>
                            <td>
                                <span th:if="${note.status == 'published'}" class="badge bg-success">已发布</span>
                                <span th:if="${note.status == 'draft'}" class="badge bg-warning">草稿</span>
                            </td>
                            <td th:text="${note.viewCount}">0</td>
                            <td th:text="${#temporals.format(note.createTime, 'yyyy-MM-dd')}">发布时间</td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a th:href="@{/note/view/{id}(id=${note.id})}" 
                                       class="btn btn-outline-primary" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a th:href="@{/note/edit/{id}(id=${note.id})}" 
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a th:href="@{/note/delete/{id}(id=${note.id})}" 
                                       class="btn btn-outline-danger" title="删除"
                                       onclick="return confirm('确定要删除这篇心得吗？此操作不可恢复。')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>阅读心得分享平台</h5>
                <p>分享阅读，传递智慧</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>&copy; 2023 阅读心得分享平台 版权所有</p>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
<script th:src="@{/js/main.js}"></script>
</body>
</html>
