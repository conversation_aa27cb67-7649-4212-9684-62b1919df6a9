<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${note.id != null ? '编辑心得' : '发布心得'} + ' - 阅读心得分享平台'">发布心得 - 阅读心得分享平台</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" th:href="@{/}">阅读心得分享</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/}">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/note/list}">心得列表</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/note/create}">发布心得</a>
                </li>
            </ul>
            <div class="d-flex">
                <div class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span sec:authentication="name">用户名</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" th:href="@{/user/profile}">个人资料</a></li>
                        <li><a class="dropdown-item" th:href="@{/note/my-notes}">我的心得</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form th:action="@{/logout}" method="post">
                                <button class="dropdown-item" type="submit">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 th:text="${note.id != null ? '编辑心得' : '发布心得'}">发布心得</h3>
                </div>
                <div class="card-body">
                    <form th:action="${note.id != null ? '/note/edit/' + note.id : '/note/create'}" 
                          method="post" th:object="${note}" enctype="multipart/form-data" class="needs-validation" novalidate>
                        
                        <!-- 心得标题 -->
                        <div class="mb-3">
                            <label for="title" class="form-label">心得标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" th:field="*{title}" 
                                   maxlength="100" required>
                            <div class="invalid-feedback">请输入心得标题</div>
                            <div class="form-text">
                                <span id="titleCount">0/100</span>
                            </div>
                        </div>

                        <!-- 书籍信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bookName" class="form-label">书名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bookName" th:field="*{bookName}" 
                                           maxlength="100" required>
                                    <div class="invalid-feedback">请输入书名</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bookAuthor" class="form-label">作者 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bookAuthor" th:field="*{bookAuthor}" 
                                           maxlength="100" required>
                                    <div class="invalid-feedback">请输入作者</div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类 -->
                        <div class="mb-3">
                            <label for="categoryId" class="form-label">分类 <span class="text-danger">*</span></label>
                            <select class="form-select" id="categoryId" th:field="*{categoryId}" required>
                                <option value="">请选择分类</option>
                                <option th:each="category : ${categories}" 
                                        th:value="${category.id}" 
                                        th:text="${category.name}">分类名称</option>
                            </select>
                            <div class="invalid-feedback">请选择分类</div>
                        </div>

                        <!-- 封面图片 -->
                        <div class="mb-3">
                            <label for="coverFile" class="form-label">封面图片</label>
                            <input type="file" class="form-control" id="coverFile" name="coverFile" 
                                   accept="image/*" onchange="previewImage(this)">
                            <div class="form-text">支持 JPG、PNG 格式，建议尺寸 800x600</div>
                            <div class="mt-2" th:if="${note.coverImage}">
                                <img th:src="${note.coverImage}" id="imagePreview" class="img-thumbnail" 
                                     style="max-width: 200px; max-height: 150px;">
                            </div>
                            <div class="mt-2" th:unless="${note.coverImage}">
                                <img id="imagePreview" class="img-thumbnail" 
                                     style="max-width: 200px; max-height: 150px; display: none;">
                            </div>
                        </div>

                        <!-- 心得内容 -->
                        <div class="mb-3">
                            <label for="content" class="form-label">心得内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" th:field="*{content}" 
                                      rows="15" maxlength="5000" required 
                                      onkeyup="updateCharCount(this, 'contentCount')"></textarea>
                            <div class="invalid-feedback">请输入心得内容</div>
                            <div class="form-text">
                                <span id="contentCount">0/5000</span>
                            </div>
                        </div>

                        <!-- 发布状态 -->
                        <div class="mb-3">
                            <label for="status" class="form-label">发布状态</label>
                            <select class="form-select" id="status" th:field="*{status}">
                                <option value="draft">保存为草稿</option>
                                <option value="published">立即发布</option>
                            </select>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-between">
                            <a th:href="@{/note/my-notes}" class="btn btn-secondary">取消</a>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <span th:text="${note.id != null ? '更新心得' : '发布心得'}">发布心得</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>阅读心得分享平台</h5>
                <p>分享阅读，传递智慧</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>&copy; 2023 阅读心得分享平台 版权所有</p>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/main.js}"></script>
<script>
// 初始化字符计数
document.addEventListener('DOMContentLoaded', function() {
    var titleInput = document.getElementById('title');
    var contentTextarea = document.getElementById('content');
    
    if (titleInput) {
        updateCharCount(titleInput, 'titleCount');
        titleInput.addEventListener('input', function() {
            updateCharCount(this, 'titleCount');
        });
    }
    
    if (contentTextarea) {
        updateCharCount(contentTextarea, 'contentCount');
    }
});
</script>
</body>
</html>
