<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复管理员登录</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #0056b3; }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #1e7e34; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .loading { text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 管理员登录快速修复工具</h1>
        
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7; margin-bottom: 20px;">
            <h4>问题描述：</h4>
            <p>管理员用户 (admin) 无法登录，但普通用户可以正常登录。</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn btn-success" onclick="quickFix()" style="font-size: 18px; padding: 15px 30px;">
                🚀 一键修复管理员登录
            </button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="/" class="btn">返回首页</a>
            <a href="/login" class="btn">登录页面</a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #e9ecef; border-radius: 5px;">
            <h5>修复后的登录信息：</h5>
            <p><strong>用户名：</strong> admin</p>
            <p><strong>密码：</strong> admin123</p>
        </div>
    </div>

    <script>
        function quickFix() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">🔄 正在修复中，请稍候...</div>';
            
            fetch('/admin-fix/quick-fix')
                .then(response => response.text())
                .then(data => {
                    resultDiv.innerHTML = data;
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div style="background-color: #f8d7da; padding: 15px; border-radius: 5px;">❌ 修复失败: ' + error + '</div>';
                });
        }
    </script>
</body>
</html>
