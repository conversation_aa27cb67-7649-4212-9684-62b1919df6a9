<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读心得分享平台</title>

    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//cdn.bootcdn.net">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" as="style">
    <link rel="preload" th:href="@{/css/main.css}" as="style">

    <!-- 样式表 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" th:href="@{/}">阅读心得分享</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/}">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/note/list}">心得列表</a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        分类
                    </a>
                    <ul class="dropdown-menu">
                        <li th:each="category : ${categories}">
                            <a class="dropdown-item" th:href="@{/note/category/{id}(id=${category.id})}"
                               th:text="${category.name}"></a>
                        </li>
                    </ul>
                </li>
                <li class="nav-item" sec:authorize="isAuthenticated()">
                    <a class="nav-link" th:href="@{/note/create}">发布心得</a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link" th:href="@{/admin/dashboard}">管理后台</a>
                </li>
            </ul>
            <div class="d-flex">
                <div sec:authorize="!isAuthenticated()">
                    <a th:href="@{/login}" class="btn btn-outline-light me-2">登录</a>
                    <a th:href="@{/register}" class="btn btn-light">注册</a>
                </div>
                <div sec:authorize="isAuthenticated()" class="dropdown">
                    <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span sec:authentication="name">用户名</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" th:href="@{/user/profile}">个人资料</a></li>
                        <li><a class="dropdown-item" th:href="@{/note/my-notes}">我的心得</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form th:action="@{/logout}" method="post">
                                <button class="dropdown-item" type="submit">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<div class="container mt-4">
    <!-- 欢迎信息 -->
    <div class="jumbotron p-5 mb-4 bg-light rounded-3">
        <div class="container-fluid py-4">
            <h1 class="display-5 fw-bold">欢迎来到阅读心得分享平台</h1>
            <p class="col-md-8 fs-4">在这里，您可以分享您的读书心得，与他人交流阅读体验，发现更多好书。</p>
            <a sec:authorize="!isAuthenticated()" th:href="@{/register}" class="btn btn-primary btn-lg">立即注册</a>
            <a sec:authorize="isAuthenticated()" th:href="@{/note/create}" class="btn btn-primary btn-lg">发布心得</a>
        </div>
    </div>

    <!-- 最新心得 -->
    <h2 class="mb-4">最新心得</h2>
    <div class="row row-cols-1 row-cols-md-3 g-4">
        <div class="col" th:each="note : ${latestNotes}">
            <div class="card h-100">
                <img th:if="${note.coverImage}" th:src="${note.coverImage}" class="card-img-top" alt="封面图">
                <img th:unless="${note.coverImage}" th:src="@{/images/default-cover.jpg}" class="card-img-top" alt="默认封面">
                <div class="card-body">
                    <h5 class="card-title" th:text="${note.title}">心得标题</h5>
                    <p class="card-text text-muted">
                        <small>
                            <span th:text="${note.bookName}">书名</span> -
                            <span th:text="${note.bookAuthor}">作者</span>
                        </small>
                    </p>
                    <p class="card-text" th:text="${#strings.abbreviate(note.content, 100)}">心得内容摘要...</p>
                </div>
                <div class="card-footer">
                    <small class="text-muted">
                        <span th:text="${#temporals.format(note.createTime, 'yyyy-MM-dd')}">发布日期</span> |
                        <span th:text="${note.viewCount}">0</span> 次浏览
                    </small>
                    <a th:href="@{/note/view/{id}(id=${note.id})}" class="btn btn-sm btn-outline-primary float-end">阅读全文</a>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a th:href="@{/note/list}" class="btn btn-outline-primary">查看更多心得</a>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-dark text-white mt-5 py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>阅读心得分享平台</h5>
                <p>分享阅读，传递智慧</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>&copy; 2023 阅读心得分享平台 版权所有</p>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/main.js}"></script>
<script th:src="@{/js/performance-monitor.js}"></script>
</body>
</html>