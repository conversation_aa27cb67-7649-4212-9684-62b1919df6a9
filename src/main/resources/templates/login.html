<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 阅读心得分享平台</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
</head>
<body class="bg-light">
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h3>用户登录</h3>
                </div>
                <div class="card-body">
                    <div th:if="${param.error}" class="alert alert-danger">
                        用户名或密码错误
                    </div>
                    <div th:if="${param.logout}" class="alert alert-success">
                        您已成功退出登录
                    </div>
                    <div th:if="${param.registered}" class="alert alert-success">
                        注册成功，请登录
                    </div>
                    
                    <form th:action="@{/login}" method="post">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember-me" name="remember-me">
                            <label class="form-check-label" for="remember-me">记住我</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">还没有账号？ <a th:href="@{/register}">立即注册</a></p>
                    <p class="mb-0"><a th:href="@{/}">返回首页</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
