<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.CommentMapper">
    
    <resultMap id="commentResultMap" type="com.coding24h.reading_share.entity.Comment">
        <id property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="noteId" column="note_id"/>
        <result property="userId" column="user_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <association property="user" javaType="com.coding24h.reading_share.entity.User">
            <id property="id" column="user_id"/>
            <result property="username" column="username"/>
            <result property="nickname" column="nickname"/>
            <result property="avatar" column="avatar"/>
        </association>
        <association property="note" javaType="com.coding24h.reading_share.entity.Note">
            <id property="id" column="note_id"/>
            <result property="title" column="note_title"/>
        </association>
        <association property="parentComment" javaType="com.coding24h.reading_share.entity.Comment">
            <id property="id" column="parent_id"/>
            <result property="content" column="parent_content"/>
        </association>
    </resultMap>
    
    <select id="findById" resultMap="commentResultMap">
        SELECT c.*, 
               u.username, u.nickname, u.avatar,
               n.title as note_title,
               pc.content as parent_content
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes n ON c.note_id = n.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        WHERE c.id = #{id}
    </select>
    
    <select id="findByNoteId" resultMap="commentResultMap">
        SELECT c.*, 
               u.username, u.nickname, u.avatar,
               n.title as note_title,
               pc.content as parent_content
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes n ON c.note_id = n.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        WHERE c.note_id = #{noteId}
        ORDER BY c.create_time ASC
    </select>
    
    <select id="findByUserId" resultMap="commentResultMap">
        SELECT c.*, 
               u.username, u.nickname, u.avatar,
               n.title as note_title,
               pc.content as parent_content
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes n ON c.note_id = n.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        WHERE c.user_id = #{userId}
        ORDER BY c.create_time DESC
    </select>
    
    <select id="findByParentId" resultMap="commentResultMap">
        SELECT c.*, 
               u.username, u.nickname, u.avatar,
               n.title as note_title,
               pc.content as parent_content
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes n ON c.note_id = n.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        WHERE c.parent_id = #{parentId}
        ORDER BY c.create_time ASC
    </select>
    
    <select id="findAll" resultMap="commentResultMap">
        SELECT c.*, 
               u.username, u.nickname, u.avatar,
               n.title as note_title,
               pc.content as parent_content
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes n ON c.note_id = n.id
        LEFT JOIN comments pc ON c.parent_id = pc.id
        ORDER BY c.create_time DESC
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO comments (
            content, note_id, user_id, parent_id, create_time, update_time
        ) VALUES (
            #{content}, #{noteId}, #{userId}, #{parentId}, NOW(), NOW()
        )
    </insert>
    
    <update id="update">
        UPDATE comments
        SET content = #{content},
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <delete id="delete">
        DELETE FROM comments WHERE id = #{id}
    </delete>
    
    <select id="countByNoteId" resultType="int">
        SELECT COUNT(*) FROM comments WHERE note_id = #{noteId}
    </select>
    
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*) FROM comments WHERE user_id = #{userId}
    </select>
</mapper>
