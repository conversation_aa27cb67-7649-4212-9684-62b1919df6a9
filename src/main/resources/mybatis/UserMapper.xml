<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.UserMapper">
    
    <resultMap id="userResultMap" type="com.coding24h.reading_share.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="nickname" column="nickname"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="enabled" column="enabled"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <collection property="roles" ofType="com.coding24h.reading_share.entity.Role">
            <id property="id" column="role_id"/>
            <result property="name" column="role_name"/>
            <result property="description" column="role_description"/>
        </collection>
    </resultMap>
    
    <select id="findById" resultMap="userResultMap">
        SELECT u.*, 
               r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.id = #{id}
    </select>
    
    <select id="findByUsername" resultMap="userResultMap">
        SELECT u.*, 
               r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.username = #{username}
    </select>
    
    <select id="findAll" resultMap="userResultMap">
        SELECT u.*, 
               r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        ORDER BY u.create_time DESC
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            username, password, nickname, email, avatar, enabled, create_time, update_time
        ) VALUES (
            #{username}, #{password}, #{nickname}, #{email}, #{avatar}, #{enabled}, NOW(), NOW()
        )
    </insert>
    
    <update id="update">
        UPDATE users
        SET nickname = #{nickname},
            email = #{email},
            <if test="password != null">
            password = #{password},
            </if>
            <if test="avatar != null">
            avatar = #{avatar},
            </if>
            <if test="enabled != null">
            enabled = #{enabled},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <delete id="delete">
        DELETE FROM users WHERE id = #{id}
    </delete>
</mapper>
