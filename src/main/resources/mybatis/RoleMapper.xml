<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.RoleMapper">
    
    <resultMap id="roleResultMap" type="com.coding24h.reading_share.entity.Role">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
    </resultMap>
    
    <select id="findById" resultMap="roleResultMap">
        SELECT * FROM roles WHERE id = #{id}
    </select>
    
    <select id="findByName" resultMap="roleResultMap">
        SELECT * FROM roles WHERE name = #{name}
    </select>
    
    <select id="findAll" resultMap="roleResultMap">
        SELECT * FROM roles ORDER BY id
    </select>
    
    <select id="findByUserId" resultMap="roleResultMap">
        SELECT r.* 
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO roles (name, description) 
        VALUES (#{name}, #{description})
    </insert>
    
    <update id="update">
        UPDATE roles
        SET name = #{name},
            description = #{description}
        WHERE id = #{id}
    </update>
    
    <delete id="delete">
        DELETE FROM roles WHERE id = #{id}
    </delete>
    
    <insert id="assignRoleToUser">
        INSERT INTO user_roles (user_id, role_id) 
        VALUES (#{userId}, #{roleId})
    </insert>
    
    <delete id="removeRoleFromUser">
        DELETE FROM user_roles 
        WHERE user_id = #{userId} AND role_id = #{roleId}
    </delete>
</mapper>
