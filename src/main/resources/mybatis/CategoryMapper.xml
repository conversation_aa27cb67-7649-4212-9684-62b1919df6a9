<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.CategoryMapper">
    
    <resultMap id="categoryResultMap" type="com.coding24h.reading_share.entity.Category">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <select id="findById" resultMap="categoryResultMap">
        SELECT * FROM note_categories WHERE id = #{id}
    </select>
    
    <select id="findAll" resultMap="categoryResultMap">
        SELECT * FROM note_categories ORDER BY create_time DESC
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO note_categories (
            name, description, create_user_id, create_time, update_time
        ) VALUES (
            #{name}, #{description}, #{createUserId}, NOW(), NOW()
        )
    </insert>
    
    <update id="update">
        UPDATE note_categories
        SET name = #{name},
            description = #{description},
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <delete id="delete">
        DELETE FROM note_categories WHERE id = #{id}
    </delete>
</mapper>
