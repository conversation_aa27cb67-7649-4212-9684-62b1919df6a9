<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.NoteMapper">
    
    <resultMap id="noteResultMap" type="com.coding24h.reading_share.entity.Note">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="coverImage" column="cover_image"/>
        <result property="bookName" column="book_name"/>
        <result property="bookAuthor" column="book_author"/>
        <result property="categoryId" column="category_id"/>
        <result property="userId" column="user_id"/>
        <result property="status" column="status"/>
        <result property="viewCount" column="view_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <association property="user" javaType="com.coding24h.reading_share.entity.User">
            <id property="id" column="user_id"/>
            <result property="username" column="username"/>
            <result property="nickname" column="nickname"/>
            <result property="avatar" column="avatar"/>
        </association>
        <association property="category" javaType="com.coding24h.reading_share.entity.Category">
            <id property="id" column="category_id"/>
            <result property="name" column="category_name"/>
        </association>
    </resultMap>
    
    <select id="findById" resultMap="noteResultMap">
        SELECT n.*, 
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.id = #{id}
    </select>
    
    <select id="findAll" resultMap="noteResultMap">
        SELECT n.*, 
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        ORDER BY n.create_time DESC
    </select>
    
    <select id="findAllPublished" resultMap="noteResultMap">
        SELECT n.*,
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.status = 'published'
        ORDER BY n.create_time DESC
    </select>

    <select id="findAllPublishedWithPaging" resultMap="noteResultMap">
        SELECT n.*,
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.status = 'published'
        ORDER BY n.create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>
    
    <select id="findByUserId" resultMap="noteResultMap">
        SELECT n.*, 
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.user_id = #{userId}
        ORDER BY n.create_time DESC
    </select>
    
    <select id="findByCategoryId" resultMap="noteResultMap">
        SELECT n.*,
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.category_id = #{categoryId} AND n.status = 'published'
        ORDER BY n.create_time DESC
    </select>

    <select id="findByCategoryIdWithPaging" resultMap="noteResultMap">
        SELECT n.*,
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.category_id = #{categoryId} AND n.status = 'published'
        ORDER BY n.create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>
    
    <select id="findLatest" resultMap="noteResultMap">
        SELECT n.*, 
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.status = 'published'
        ORDER BY n.create_time DESC
        LIMIT #{limit}
    </select>
    
    <select id="findRelated" resultMap="noteResultMap">
        SELECT n.*, 
               u.username, u.nickname, u.avatar,
               c.name as category_name
        FROM reading_notes n
        LEFT JOIN users u ON n.user_id = u.id
        LEFT JOIN note_categories c ON n.category_id = c.id
        WHERE n.category_id = #{categoryId} 
          AND n.id != #{noteId}
          AND n.status = 'published'
        ORDER BY n.create_time DESC
        LIMIT #{limit}
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reading_notes (
            title, content, cover_image, book_name, book_author, 
            category_id, user_id, status, view_count, create_time, update_time
        ) VALUES (
            #{title}, #{content}, #{coverImage}, #{bookName}, #{bookAuthor}, 
            #{categoryId}, #{userId}, #{status}, 0, NOW(), NOW()
        )
    </insert>
    
    <update id="update">
        UPDATE reading_notes
        SET title = #{title},
            content = #{content},
            <if test="coverImage != null">
            cover_image = #{coverImage},
            </if>
            book_name = #{bookName},
            book_author = #{bookAuthor},
            category_id = #{categoryId},
            status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <delete id="delete">
        DELETE FROM reading_notes WHERE id = #{id}
    </delete>
    
    <update id="incrementViewCount">
        UPDATE reading_notes
        SET view_count = view_count + 1
        WHERE id = #{id}
    </update>
    
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*) FROM reading_notes WHERE user_id = #{userId}
    </select>
    
    <select id="countByCategoryId" resultType="int">
        SELECT COUNT(*) FROM reading_notes WHERE category_id = #{categoryId}
    </select>

    <select id="countAllPublished" resultType="int">
        SELECT COUNT(*) FROM reading_notes WHERE status = 'published'
    </select>

    <select id="countByCategoryIdPublished" resultType="int">
        SELECT COUNT(*) FROM reading_notes WHERE category_id = #{categoryId} AND status = 'published'
    </select>
</mapper>