-- 创建数据库
CREATE DATABASE reading_share CHARACTER SET utf8mb4;
USE reading_share;
-- MySQL dump 10.13  Distrib 9.2.0, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: reading_share
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `book_category`
--

DROP TABLE IF EXISTS `book_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category_name` varchar(32) NOT NULL COMMENT '分类名称（如文学/科幻/哲学）',
  `category_alias` varchar(32) NOT NULL COMMENT '分类别名',
  `create_user` int unsigned NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `fk_book_category_user` (`create_user`),
  CONSTRAINT `fk_book_category_user` FOREIGN KEY (`create_user`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='书籍分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `book_category`
--

LOCK TABLES `book_category` WRITE;
/*!40000 ALTER TABLE `book_category` DISABLE KEYS */;
INSERT INTO `book_category` VALUES (1,'文学经典作品','classic',1,'2025-06-01 23:56:49','2025-06-05 20:01:09'),(2,'文学','Literature',1,'2025-06-01 23:57:41','2025-06-05 18:51:52'),(3,'科幻','SF',1,'2025-06-01 23:57:51','2025-06-01 23:57:51'),(4,'历史','History',1,'2025-06-01 23:58:00','2025-06-01 23:58:00'),(6,'恐怖','Horror Fiction',1,'2025-06-01 23:59:54','2025-06-01 23:59:54'),(7,'教育','Education',1,'2025-06-02 00:00:15','2025-06-02 00:00:15'),(8,'体育','Sports',1,'2025-06-02 00:00:31','2025-06-02 00:00:31'),(11,'哲学','Sociology',3,'2025-06-02 11:08:32','2025-06-02 11:08:32'),(15,'社会学','Sociology',1,'2025-06-04 13:58:11','2025-06-04 13:58:11');
/*!40000 ALTER TABLE `book_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `book_note`
--

DROP TABLE IF EXISTS `book_note`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_note` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '心得ID',
  `title` varchar(30) NOT NULL COMMENT '心得标题',
  `content` varchar(10000) NOT NULL COMMENT '心得内容',
  `cover_img` varchar(128) NOT NULL COMMENT '封面图片',
  `state` varchar(3) DEFAULT '草稿' COMMENT '状态: [已发布/草稿]',
  `category_id` int unsigned NOT NULL COMMENT '分类ID',
  `book_name` varchar(50) NOT NULL COMMENT '书名',
  `author` varchar(30) NOT NULL COMMENT '作者',
  `create_user` int unsigned NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `fk_book_note_category` (`category_id`),
  KEY `fk_book_note_user` (`create_user`),
  CONSTRAINT `fk_book_note_category` FOREIGN KEY (`category_id`) REFERENCES `book_category` (`id`),
  CONSTRAINT `fk_book_note_user` FOREIGN KEY (`create_user`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='读书心得表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `book_note`
--

LOCK TABLES `book_note` WRITE;
/*!40000 ALTER TABLE `book_note` DISABLE KEYS */;
INSERT INTO `book_note` VALUES (1,'余华治好我的精神内耗','余华笔下的《活着》，是生命在苦难中开出的坚韧之花。福贵历经亲人离世、命运跌宕，却始终以沉默对抗无常。那些被厄运反复捶打的日子里，他对活着本身的执着，让我读懂生命的重量不在于荣华，而在于承受后的坚守。每一次阅读，都是对 “活着” 二字的重新叩问——或许活着的意义，就藏在继续活下去的勇气里。','http://example.com/cover.jpg','草稿',1,'活着','余华',3,'2025-06-02 11:10:00','2025-06-04 15:22:50'),(2,'读《活着》有感','余华笔下的《活着》，是生命在苦难中开出的坚韧之花。福贵历经亲人离世、命运跌宕，却始终以沉默对抗无常。那些被厄运反复捶打的日子里，他对活着本身的执着，让我读懂生命的重量不在于荣华，而在于承受后的坚守。每一次阅读，都是对 “活着” 二字的重新叩问——或许活着的意义，就藏在继续活下去的勇气里。','http://example.com/cover.jpg','已发布',1,'活着','余华',3,'2025-06-02 12:53:52','2025-06-02 12:53:52'),(5,'读《活着》有感','余华笔下的《活着》，是生命在苦难中开出的坚韧之花。福贵历经亲人离世、命运跌宕，却始终以沉默对抗无常。那些被厄运反复捶打的日子里，他对活着本身的执着，让我读懂生命的重量不在于荣华，而在于承受后的坚守。每一次阅读，都是对 “活着” 二字的重新叩问——或许活着的意义，就藏在继续活下去的勇气里。','http://example.com/cover.jpg','已发布',1,'活着','余华',3,'2025-06-02 21:17:00','2025-06-02 21:17:00'),(6,'活着治好我的内耗','活着写的很好。。。。。。。。。。。','http://localhost:8080/20250603-ffffc1f6-988d-46c7-898b-ad5c4d7df4f2.jpg','已发布',1,'活着','余华',3,'2025-06-03 23:50:18','2025-06-03 23:50:18'),(7,'读《活着》','余华笔下的《活着》，是生命在苦难中开出的坚韧之花。福贵历经亲人离世、命运跌宕，却始终以沉默对抗无常。那些被厄运反复捶打的日子里，他对活着本身的执着，让我读懂生命的重量不在于荣华，而在于承受后的坚守。每一次阅读，都是对 “活着” 二字的重新叩问——或许活着的意义，就藏在继续活下去的勇气里。','http://localhost:8080/20250605-4d7487f3-44e1-4908-9c01-68461f59dcb4.jpg','草稿',1,'活着','余华',3,'2025-06-04 14:49:42','2025-06-05 21:17:58'),(13,'读《水浒传》有感','《水浒传》是一曲英雄主义的悲壮长歌。书中 108 位好汉虽性格迥异，却都怀揣侠义之心：武松打虎显勇毅，林冲雪夜彰孤愤，鲁智深倒拔垂杨柳见豪情。他们替天行道却难逃宿命，招安后的悲剧折射出封建时代的无奈。书中不仅有刀光剑影的热血，更藏着对忠义与现实冲突的深刻叩问，让我在感叹英雄豪情时，亦深思人性与时代的枷锁。','http://localhost:8080/20250605-17f31846-8210-41aa-a3fe-a32f9c9a20f4.webp','草稿',1,'水浒传','施耐庵',3,'2025-06-05 18:15:36','2025-06-05 20:49:59'),(14,'读《活着》有感','余华笔下的《活着》，是生命在苦难中开出的坚韧之花。福贵历经亲人离世、命运跌宕，却始终以沉默对抗无常。那些被厄运反复捶打的日子里，他对活着本身的执着，让我读懂生命的重量不在于荣华，而在于承受后的坚守。每一次阅读，都是对 “活着” 二字的重新叩问——或许活着的意义，就藏在继续活下去的勇气里。','http://example.com/cover.jpg','已发布',1,'活着','余华',1,'2025-06-05 19:57:57','2025-06-05 19:57:57'),(15,'读《水浒传》有感','\n读《水浒传》有感\n《水浒传》是一曲英雄主义的悲壮长歌。书中 108 位好汉虽性格迥异，却都怀揣侠义之心：武松打虎显勇毅，林冲雪夜彰孤愤，鲁智深倒拔垂杨柳见豪情。他们替天行道却难逃宿命，招安后的悲剧折射出封建时代的无奈。书中不仅有刀光剑影的热血，更藏着对忠义与现实冲突的深刻叩问，让我在感叹英雄豪情时，亦深思人性与时代的枷锁。\n','http://localhost:8080/20250605-c471a61d-f122-4a0e-ae2e-fca0f90af89e.webp','已发布',1,'水浒传','施耐庵',1,'2025-06-05 19:58:05','2025-06-05 19:58:05'),(18,'水浒传有感','《水浒传》是一曲英雄主义的悲壮长歌。书中 108 位好汉虽性格迥异，却都怀揣侠义之心：武松打虎显勇毅，林冲雪夜彰孤愤，鲁智深倒拔垂杨柳见豪情。他们替天行道却难逃宿命，招安后的悲剧折射出封建时代的无奈。书中不仅有刀光剑影的热血，更藏着对忠义与现实冲突的深刻叩问，让我在感叹英雄豪情时，亦深思人性与时代的枷锁。','http://localhost:8080/20250605-ce57f0d0-5fd6-4345-b77f-e477a59498a9.webp','已发布',1,'水浒传','施耐庵',1,'2025-06-05 20:51:27','2025-06-05 20:51:27');
/*!40000 ALTER TABLE `book_note` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comment`
--

DROP TABLE IF EXISTS `comment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comment` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `content` varchar(1000) NOT NULL COMMENT '评论内容',
  `note_id` int unsigned NOT NULL COMMENT '心得ID',
  `user_id` int unsigned NOT NULL COMMENT '用户ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_comment_note` (`note_id`),
  KEY `fk_comment_user` (`user_id`),
  CONSTRAINT `fk_comment_note` FOREIGN KEY (`note_id`) REFERENCES `book_note` (`id`),
  CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评论表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comment`
--

LOCK TABLES `comment` WRITE;
/*!40000 ALTER TABLE `comment` DISABLE KEYS */;
INSERT INTO `comment` VALUES (1,'这本书确实是值得看',1,3,'2025-06-02 00:07:08','2025-06-04 08:40:28'),(3,'这本书的观点很新颖，值得一读！',1,3,'2025-06-02 00:40:25','2025-06-02 00:40:25'),(4,'这本书的观点很新颖',2,3,'2025-06-02 09:14:44','2025-06-02 09:14:44'),(5,'这本书的观点很新颖',1,3,'2025-06-02 09:43:47','2025-06-02 09:43:47'),(6,'这本书的观点很新颖',1,3,'2025-06-03 08:16:19','2025-06-03 08:16:19'),(8,'这本书的观点很新颖',1,3,'2025-06-03 23:57:46','2025-06-03 23:57:46'),(9,'这本书的观点很新颖',1,3,'2025-06-04 08:32:36','2025-06-04 08:32:36'),(10,'这本书的观点很新颖',1,3,'2025-06-04 08:32:58','2025-06-04 08:32:58'),(11,'这本书的观点很新颖',1,3,'2025-06-04 08:38:50','2025-06-04 08:38:50');
/*!40000 ALTER TABLE `comment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(20) NOT NULL COMMENT '用户名',
  `password` varchar(32) DEFAULT NULL COMMENT '密码',
  `nickname` varchar(10) DEFAULT '' COMMENT '昵称',
  `email` varchar(128) DEFAULT '' COMMENT '邮箱',
  `user_pic` varchar(128) DEFAULT '' COMMENT '头像',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'chen','8d2c5c1843b84379aa5ef5474b4e7e97','陈22','<EMAIL>','http://localhost:8080/20250605-670c947f-c345-4fba-9d9c-18fb75ed442b.jpeg','2025-06-01 23:56:05','2025-06-05 20:37:24'),(2,'chenxiao','8d2c5c1843b84379aa5ef5474b4e7e97','','','','2025-06-01 23:56:10','2025-06-01 23:56:10'),(3,'chenxt','8d2c5c1843b84379aa5ef5474b4e7e97','我是陈晓通','<EMAIL>','http://localhost:8080/20250605-8aa1e9c2-0ceb-483b-a685-d5dea189e539.jpg','2025-06-01 23:56:15','2025-06-05 21:14:45'),(4,'chenxiaot','8d2c5c1843b84379aa5ef5474b4e7e97','','','','2025-06-03 21:47:19','2025-06-03 21:47:19'),(5,'chenxiaotong','30f0faa609b679861dbbcaaac4e9c70d','chenxt','<EMAIL>','https://spring.io/img/extra/bakker-120.jpg','2025-06-03 22:07:57','2025-06-04 13:10:21'),(6,'chenxiaotongg','8d2c5c1843b84379aa5ef5474b4e7e97','','','','2025-06-03 23:45:32','2025-06-03 23:45:32'),(7,'chenx','8d2c5c1843b84379aa5ef5474b4e7e97','陈11','<EMAIL>','http://localhost:8080/20250605-3a6182c9-73d6-46e9-a408-0b547bdde50b.jpeg','2025-06-05 19:04:12','2025-06-05 19:05:00'),(8,'chenxx','8d2c5c1843b84379aa5ef5474b4e7e97','111','<EMAIL>','http://localhost:8080/20250605-b38b1aa8-555e-4d26-8caf-9f1b03a715f3.jpg','2025-06-05 19:47:02','2025-06-05 20:39:01'),(9,'chenxtt','8d2c5c1843b84379aa5ef5474b4e7e97','','','','2025-06-05 20:29:27','2025-06-05 20:29:27'),(10,'chenc','8d2c5c1843b84379aa5ef5474b4e7e97','','','','2025-06-05 20:44:25','2025-06-05 20:44:25');
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-05 22:21:47
