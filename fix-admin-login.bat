@echo off
echo ========================================
echo 修复管理员登录问题
echo ========================================
echo.

echo 正在连接数据库并执行修复脚本...
echo.

mysql -u root -p1234 -e "USE reading_share; INSERT IGNORE INTO users (username, password, nickname, enabled, create_time, update_time) VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1, NOW(), NOW()); INSERT IGNORE INTO roles (name, description) VALUES ('ROLE_ADMIN', '管理员'), ('ROLE_EDITOR', '编辑'), ('ROLE_USER', '普通用户'); SET @admin_user_id = (SELECT id FROM users WHERE username = 'admin'); SET @admin_role_id = (SELECT id FROM roles WHERE name = 'ROLE_ADMIN'); INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (@admin_user_id, @admin_role_id); UPDATE users SET enabled = 1 WHERE username = 'admin';"

echo.
echo 验证修复结果...
mysql -u root -p1234 -e "USE reading_share; SELECT u.id, u.username, u.nickname, u.enabled, r.name as role_name FROM users u LEFT JOIN user_roles ur ON u.id = ur.user_id LEFT JOIN roles r ON ur.role_id = r.id WHERE u.username = 'admin';"

echo.
echo ========================================
echo 修复完成！
echo 管理员登录信息：
echo 用户名: admin
echo 密码: admin123
echo ========================================
pause
