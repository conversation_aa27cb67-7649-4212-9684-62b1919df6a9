-- 创建数据库
CREATE DATABASE reading_share CHARACTER SET utf8mb4;
USE reading_share;

-- 用户表
CREATE TABLE `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码(加密存储)',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用: 1-启用, 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
CREATE TABLE `roles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(100) DEFAULT NULL COMMENT '角色描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE `user_roles` (
  `user_id` int unsigned NOT NULL COMMENT '用户ID',
  `role_id` int unsigned NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `fk_user_roles_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 心得分类表
CREATE TABLE `note_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `create_user_id` int unsigned NOT NULL COMMENT '创建用户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `fk_note_categories_user_id` (`create_user_id`),
  CONSTRAINT `fk_note_categories_user_id` FOREIGN KEY (`create_user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心得分类表';

-- 心得表
CREATE TABLE `reading_notes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '心得ID',
  `title` varchar(100) NOT NULL COMMENT '心得标题',
  `content` text NOT NULL COMMENT '心得内容',
  `cover_image` varchar(200) DEFAULT NULL COMMENT '封面图片URL',
  `book_name` varchar(100) NOT NULL COMMENT '书名',
  `book_author` varchar(100) NOT NULL COMMENT '书籍作者',
  `category_id` int unsigned NOT NULL COMMENT '分类ID',
  `user_id` int unsigned NOT NULL COMMENT '创建用户ID',
  `status` enum('draft','published') NOT NULL DEFAULT 'draft' COMMENT '状态: draft-草稿, published-已发布',
  `view_count` int unsigned DEFAULT 0 COMMENT '浏览次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `fk_reading_notes_category_id` (`category_id`),
  KEY `fk_reading_notes_user_id` (`user_id`),
  CONSTRAINT `fk_reading_notes_category_id` FOREIGN KEY (`category_id`) REFERENCES `note_categories` (`id`),
  CONSTRAINT `fk_reading_notes_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='读书心得表';

-- 评论表
CREATE TABLE `comments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `content` text NOT NULL COMMENT '评论内容',
  `note_id` int unsigned NOT NULL COMMENT '关联心得ID',
  `user_id` int unsigned NOT NULL COMMENT '评论用户ID',
  `parent_id` int unsigned DEFAULT NULL COMMENT '父评论ID，用于回复功能',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `fk_comments_note_id` (`note_id`),
  KEY `fk_comments_user_id` (`user_id`),
  KEY `fk_comments_parent_id` (`parent_id`),
  CONSTRAINT `fk_comments_note_id` FOREIGN KEY (`note_id`) REFERENCES `reading_notes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_comments_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 收藏表
CREATE TABLE `favorites` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int unsigned NOT NULL COMMENT '用户ID',
  `note_id` int unsigned NOT NULL COMMENT '心得ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_note` (`user_id`,`note_id`),
  KEY `fk_favorites_note_id` (`note_id`),
  CONSTRAINT `fk_favorites_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_favorites_note_id` FOREIGN KEY (`note_id`) REFERENCES `reading_notes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';

-- 初始化角色数据
INSERT INTO `roles` (`name`, `description`) VALUES
('ROLE_ADMIN', '管理员'),
('ROLE_EDITOR', '编辑'),
('ROLE_USER', '普通用户');

-- 初始化管理员账号 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `nickname`, `enabled`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1);

-- 为管理员分配角色
INSERT INTO `user_roles` (`user_id`, `role_id`) VALUES
(1, 1);

-- 初始化心得分类
INSERT INTO `note_categories` (`name`, `description`, `create_user_id`) VALUES
('文学', '文学类读书心得', 1),
('科技', '科技类读书心得', 1),
('历史', '历史类读书心得', 1),
('哲学', '哲学类读书心得', 1),
('经济', '经济管理类读书心得', 1);

-- 初始化测试心得数据
INSERT INTO `reading_notes` (`title`, `content`, `book_name`, `book_author`, `category_id`, `user_id`, `status`, `view_count`) VALUES
('水浒传读后感', '《水浒传》是中国古典四大名著之一，描写了北宋末年以宋江为首的108位好汉在梁山起义的故事。这部作品展现了封建社会的黑暗和人民的反抗精神。\n\n通过阅读这部作品，我深深感受到了作者对社会不公的愤慨和对英雄豪杰的赞美。书中的人物形象鲜明，如武松的勇猛、林冲的忠义、李逵的率真，都给我留下了深刻印象。\n\n特别是宋江这个人物，他既有领袖的魅力，又有复杂的性格特点。他的"忠义"思想既是他的优点，也成为了他的局限性。这让我思考在面对不公时，我们应该如何选择正确的道路。', '水浒传', '施耐庵', 1, 1, 'published', 2),
('人工智能时代的思考', '随着人工智能技术的快速发展，我们正站在一个新时代的门槛上。这本书深入浅出地介绍了AI的发展历程、现状和未来趋势。\n\n作者从技术、社会、伦理等多个角度分析了AI对人类社会的影响。我特别关注的是AI对就业市场的冲击。虽然AI会取代一些工作，但也会创造新的机会。关键是我们要不断学习，适应时代的变化。\n\n书中提到的"人机协作"概念很有启发性。未来不是人与机器的对立，而是如何更好地合作。我们需要发挥人类的创造力、情感和道德判断能力，这些是机器难以替代的。', '人工智能时代', '李开复', 2, 1, 'published', 5),
('史记中的人物智慧', '司马迁的《史记》不仅是一部史书，更是一部人物传记的经典。通过阅读这部巨著，我看到了历史长河中各种人物的智慧和品格。\n\n项羽的英勇、刘邦的智谋、韩信的军事才能，每个人物都有其独特的魅力。但最让我印象深刻的是司马迁本人的史学精神。他在遭受宫刑的巨大痛苦后，仍然坚持完成这部史学巨著，这种精神令人敬佩。\n\n历史是最好的老师。通过学习历史人物的成败得失，我们可以获得人生的智慧。正如司马迁所说："究天人之际，通古今之变，成一家之言。"', '史记', '司马迁', 3, 1, 'published', 8);