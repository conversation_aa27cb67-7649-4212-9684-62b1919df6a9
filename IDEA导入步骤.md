# IntelliJ IDEA 导入和运行步骤

## 🚀 快速开始 (推荐方式)

### 步骤1: 打开IntelliJ IDEA
1. 启动IntelliJ IDEA
2. 如果是首次使用，选择 "Don't import settings"

### 步骤2: 导入项目
1. 在欢迎界面点击 **"Open"**
2. 浏览到项目文件夹 `D:\engine\reading_share`
3. 选择整个文件夹，点击 **"OK"**
4. IDEA会自动识别这是一个Maven项目

### 步骤3: 等待依赖下载
1. IDEA会自动开始下载Maven依赖
2. 右下角会显示进度条 "Importing..."
3. 等待完成（可能需要几分钟，取决于网络速度）

### 步骤4: 配置JDK
1. 如果提示JDK问题，按 `Ctrl+Alt+Shift+S` 打开项目设置
2. 选择 **Project** → **Project SDK**
3. 选择JDK 17或更高版本
4. 点击 **"Apply"** 和 **"OK"**

### 步骤5: 配置数据库
1. 确保MySQL服务已启动
2. 创建数据库：
   ```sql
   CREATE DATABASE reading_share CHARACTER SET utf8mb4;
   ```
3. 导入数据：
   ```bash
   mysql -u root -p reading_share < reading_share.sql
   ```
4. 修改配置文件 `src/main/resources/application.properties`：
   ```properties
   spring.datasource.password=your_mysql_password
   ```

### 步骤6: 运行项目
1. 在项目树中找到：
   ```
   src/main/java/com/coding24h/reading_share/ReadingShareApplication.java
   ```
2. 右键该文件
3. 选择 **"Run 'ReadingShareApplication'"**
4. 或者点击类名旁边的绿色三角形 ▶️

### 步骤7: 验证运行
1. 等待控制台显示：
   ```
   Started ReadingShareApplication in X.XXX seconds
   ```
2. 打开浏览器访问：
   - 测试页面: http://localhost:8080/test/page
   - 应用首页: http://localhost:8080/

## 🔧 常见问题解决

### 问题1: Maven依赖下载失败
**解决方案:**
1. 检查网络连接
2. 右键项目 → **Maven** → **Reload project**
3. 或者在右侧Maven面板点击刷新按钮 🔄

### 问题2: JDK版本问题
**解决方案:**
1. `File` → `Project Structure` → `Project`
2. 设置 **Project SDK** 为 JDK 17+
3. 设置 **Project language level** 为 17

### 问题3: 编码问题
**解决方案:**
1. `File` → `Settings` → `Editor` → `File Encodings`
2. 设置所有编码为 **UTF-8**

### 问题4: 数据库连接失败
**解决方案:**
1. 检查MySQL服务状态
2. 验证用户名密码
3. 确认数据库已创建并导入数据

### 问题5: 端口被占用
**解决方案:**
1. 修改 `application.properties`：
   ```properties
   server.port=8081
   ```
2. 或者关闭占用8080端口的程序

## 📋 项目结构说明

```
reading_share/
├── src/main/java/com/coding24h/reading_share/
│   ├── ReadingShareApplication.java    # 🚀 主启动类
│   ├── config/                        # ⚙️ 配置类
│   ├── controller/                    # 🎮 控制器层
│   ├── entity/                       # 📦 实体层
│   ├── mapper/                       # 🗃️ 数据访问层
│   ├── security/                     # 🔒 安全相关
│   └── service/                      # 🔧 服务层
├── src/main/resources/
│   ├── application.properties        # ⚙️ 应用配置
│   ├── mybatis/                     # 🗃️ MyBatis映射
│   ├── static/                      # 🎨 静态资源
│   └── templates/                   # 📄 页面模板
├── pom.xml                          # 📦 Maven配置
└── reading_share.sql               # 🗄️ 数据库脚本
```

## 🎯 成功运行的标志

### 控制台输出应包含：
```
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.5.0)

...
Started ReadingShareApplication in X.XXX seconds (JVM running for X.XXX)
```

### 浏览器访问正常：
- ✅ http://localhost:8080/test/page 显示测试页面
- ✅ http://localhost:8080/ 显示应用首页
- ✅ http://localhost:8080/login 显示登录页面

## 🧪 功能测试

### 1. 基础测试
- [ ] 访问测试页面确认系统运行
- [ ] 注册新用户账号
- [ ] 登录管理员账号 (admin/admin123)

### 2. 功能测试
- [ ] 发布一篇心得
- [ ] 编辑心得内容
- [ ] 添加评论
- [ ] 管理分类

## 📞 获取帮助

如果遇到问题：
1. 查看IDEA底部的 **Event Log** 面板
2. 检查 **Run** 面板的错误信息
3. 参考 `问题诊断和解决方案.md`

## 🎉 恭喜！

如果以上步骤都成功完成，您的阅读心得分享平台就已经成功运行了！

现在您可以：
- 🏠 访问首页浏览心得
- 👤 注册和登录用户
- 📝 发布和管理心得
- 💬 参与评论互动
- 🔧 使用管理员功能
