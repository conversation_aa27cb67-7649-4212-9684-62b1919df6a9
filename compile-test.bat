@echo off
echo ========================================
echo 编译测试脚本
echo ========================================
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java环境未配置
    goto :end
)

echo.
echo 尝试使用javac编译主要类...
echo.

if not exist "target\classes" mkdir "target\classes"

echo 编译实体类...
javac -d target\classes -cp "." src\main\java\com\coding24h\reading_share\entity\*.java
if %errorlevel% neq 0 (
    echo [错误] 实体类编译失败
    goto :end
)

echo [成功] 实体类编译成功
echo.

echo 注意: 完整编译需要Spring Boot依赖
echo 推荐使用IDE进行完整编译和运行
echo.

:end
pause
