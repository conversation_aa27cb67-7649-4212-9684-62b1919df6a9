-- 管理员登录问题修复脚本 v3
-- 处理外键约束问题
USE reading_share;

-- 1. 检查当前数据库状态
SELECT '=== 检查用户表 ===' as info;
SELECT id, username, enabled, password, nickname FROM users;

SELECT '=== 检查角色表 ===' as info;
SELECT * FROM roles;

-- 2. 暂时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 3. 确保基础角色存在
INSERT IGNORE INTO roles (name, description) 
VALUES 
('ROLE_ADMIN', '管理员'),
('ROLE_USER', '普通用户'),
('ROLE_EDITOR', '编辑者');

-- 4. 检查admin用户是否存在
SELECT '=== 检查admin用户 ===' as info;
SELECT * FROM users WHERE username = 'admin';

-- 5. 如果admin用户存在，先清理相关数据
DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');

-- 删除admin用户可能关联的其他数据（如果有的话）
-- 注意：这里可能需要根据实际的表结构调整
DELETE FROM notes WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');
DELETE FROM comments WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');
DELETE FROM favorites WHERE user_id IN (SELECT id FROM users WHERE username = 'admin');

-- 6. 删除admin用户
DELETE FROM users WHERE username = 'admin';

-- 7. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 8. 创建新的admin用户
-- 密码是 admin123 的BCrypt加密结果
INSERT INTO users (username, password, nickname, enabled) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1);

-- 9. 为admin用户分配管理员角色
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'ROLE_ADMIN';

-- 10. 验证结果
SELECT '=== 验证admin用户 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, r.name as role_name
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

SELECT '=== 修复完成 ===' as info;
SELECT '管理员账号: admin' as info;
SELECT '管理员密码: admin123' as info;
