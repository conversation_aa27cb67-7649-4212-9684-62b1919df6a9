-- 最简单的admin修复脚本
USE reading_share;

-- 1. 只修复角色关联，不修改nickname
DELETE FROM user_roles WHERE user_id = 1;

INSERT INTO user_roles (user_id, role_id) VALUES (1, 1);

-- 2. 只更新密码，不修改nickname
UPDATE users 
SET password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6',
    enabled = 1
WHERE username = 'admin';

-- 3. 验证结果
SELECT '=== 验证结果 ===' as info;
SELECT u.id, u.username, u.enabled, u.nickname, r.name as role_name
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

SELECT '修复完成，请使用 admin/admin123 登录' as result;
