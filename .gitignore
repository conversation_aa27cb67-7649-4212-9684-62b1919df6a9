# 通用编译产物、临时目录（整合双方对构建产物的忽略）
bin-debug/
bin-release/
[Oo]bj/
[Bb]in/
target/
!**/src/main/**/target/
!**/src/test/**/target/
build/
!**/src/main/**/build/
!**/src/test/**/build/

# 依赖管理工具相关（Maven 相关保留）
.mvn/wrapper/maven-wrapper.jar

# IDE 配置文件忽略（整合多 IDE 规则）
### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

# 可执行文件（补充常见格式）
*.swf
*.air
*.ipa
*.apk
*.exe

# 文档类（保留 HELP.md ，若不需要可删除）
HELP.md

# 其他临时文件
*.swp
.DS_Store