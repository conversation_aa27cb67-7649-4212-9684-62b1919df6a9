@echo off
echo ========================================
echo 环境检查脚本 - 阅读心得分享项目
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境
    echo 请安装JDK 17或更高版本
    echo 下载地址: https://adoptium.net/
    goto :error
) else (
    echo [成功] Java环境正常
)
echo.

echo 2. 检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 未找到Maven环境
    echo 建议安装Maven或使用IDE运行项目
    echo Maven下载: https://maven.apache.org/download.cgi
) else (
    echo [成功] Maven环境正常
    mvn -version
)
echo.

echo 3. 检查MySQL连接...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 未找到MySQL客户端
    echo 请确保MySQL已安装并启动
) else (
    echo [成功] MySQL客户端已安装
    mysql --version
)
echo.

echo 4. 检查项目文件...
if exist "pom.xml" (
    echo [成功] 找到pom.xml
) else (
    echo [错误] 未找到pom.xml，请确保在项目根目录运行
    goto :error
)

if exist "src\main\java\com\coding24h\reading_share\ReadingShareApplication.java" (
    echo [成功] 找到主启动类
) else (
    echo [错误] 未找到主启动类
    goto :error
)

if exist "reading_share.sql" (
    echo [成功] 找到数据库脚本
) else (
    echo [警告] 未找到数据库脚本
)
echo.

echo ========================================
echo 环境检查完成
echo ========================================
echo.
echo 推荐运行方式:
echo 1. 使用IDE (IntelliJ IDEA/Eclipse) - 最简单
echo 2. 使用Maven: mvn spring-boot:run
echo 3. 查看详细指南: IDE运行指南.md
echo.
echo 如果遇到问题，请查看: 问题诊断和解决方案.md
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo 环境检查失败
echo ========================================
echo 请解决上述问题后重新运行
pause
exit /b 1
