@echo off
echo ========================================
echo 阅读心得分享平台 - Java直接运行方式
echo ========================================
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 17+
    pause
    exit /b 1
)

echo.
echo 编译Java源文件...
if not exist "target\classes" mkdir "target\classes"

echo 下载依赖JAR文件...
if not exist "lib" mkdir "lib"

echo.
echo 注意: 此方式需要手动下载依赖JAR文件
echo 推荐使用以下方式之一:
echo.
echo 1. 安装Maven并运行: mvn spring-boot:run
echo 2. 使用IDE (IntelliJ IDEA/Eclipse) 直接运行
echo 3. 使用Spring Boot CLI
echo.
echo 如果您已经安装了Maven，请运行 start.bat
echo.
pause
