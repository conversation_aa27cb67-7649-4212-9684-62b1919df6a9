@echo off
echo ========================================
echo 快速编译测试 - 阅读心得分享项目
echo ========================================
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java环境未配置
    goto :end
)

echo.
echo 尝试编译主要Java文件...
echo.

if not exist "target\classes" mkdir "target\classes"

echo 1. 编译实体类...
javac -d target\classes -cp "." src\main\java\com\coding24h\reading_share\entity\*.java
if %errorlevel% neq 0 (
    echo [错误] 实体类编译失败
    goto :end
) else (
    echo [成功] 实体类编译成功
)

echo.
echo 2. 编译Mapper接口...
javac -d target\classes -cp "target\classes" src\main\java\com\coding24h\reading_share\mapper\*.java
if %errorlevel% neq 0 (
    echo [错误] Mapper接口编译失败
    goto :end
) else (
    echo [成功] Mapper接口编译成功
)

echo.
echo 3. 编译Service接口...
javac -d target\classes -cp "target\classes" src\main\java\com\coding24h\reading_share\service\*.java
if %errorlevel% neq 0 (
    echo [错误] Service接口编译失败
    goto :end
) else (
    echo [成功] Service接口编译成功
)

echo.
echo ========================================
echo 基础编译测试完成
echo ========================================
echo.
echo 注意: 这只是基础的语法检查
echo 完整编译需要Spring Boot依赖
echo 推荐使用IDE进行完整编译和运行
echo.

:end
pause
