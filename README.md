# 阅读心得分享平台

## 项目介绍

这是一个基于SSM（Spring + SpringMVC + MyBatis）架构的阅读心得分享平台，用户可以在平台上分享自己的读书心得，与其他用户交流阅读体验。

## 技术架构

### 后端技术栈
- **Spring Boot 3.5.0** - 应用框架
- **Spring Security 6** - 安全框架
- **MyBatis 3.0.4** - 持久层框架
- **MySQL 8.0** - 数据库
- **Thymeleaf** - 模板引擎
- **Lombok** - 代码简化工具

### 前端技术栈
- **Bootstrap 5.3.0** - UI框架
- **Thymeleaf** - 服务端模板引擎
- **JavaScript** - 前端交互

## 项目结构

```
src/main/java/com/coding24h/reading_share/
├── config/                 # 配置类
│   ├── SecurityConfig.java # Spring Security配置
│   └── WebConfig.java      # Web配置
├── controller/             # 控制器层
│   ├── AdminController.java
│   ├── CategoryController.java
│   ├── CommentController.java
│   ├── HomeController.java
│   ├── NoteController.java
│   └── UserController.java
├── mapper/                 # 数据访问层
│   ├── CategoryMapper.java
│   ├── CommentMapper.java
│   ├── NoteMapper.java
│   ├── RoleMapper.java
│   └── UserMapper.java
├── model/                  # 实体类
│   ├── Category.java
│   ├── Comment.java
│   ├── Note.java
│   ├── Role.java
│   └── User.java
├── security/               # 安全相关
│   ├── SecurityUtils.java
│   └── UserDetailsServiceImpl.java
└── service/                # 服务层
    ├── impl/               # 服务实现类
    │   ├── CategoryServiceImpl.java
    │   ├── CommentServiceImpl.java
    │   ├── NoteServiceImpl.java
    │   └── UserServiceImpl.java
    ├── CategoryService.java
    ├── CommentService.java
    ├── NoteService.java
    └── UserService.java
```

## 功能特性

### 用户功能
- 用户注册/登录
- 个人资料管理
- 发布/编辑/删除心得
- 浏览心得列表
- 查看心得详情
- 评论功能
- 收藏功能

### 管理功能
- 用户管理
- 心得管理
- 分类管理
- 评论管理

### 角色权限
- **普通用户(ROLE_USER)**: 基本的心得发布和评论功能
- **编辑(ROLE_EDITOR)**: 可以管理分类
- **管理员(ROLE_ADMIN)**: 拥有所有权限

## 数据库设计

主要数据表：
- `users` - 用户表
- `roles` - 角色表
- `user_roles` - 用户角色关联表
- `note_categories` - 心得分类表
- `reading_notes` - 心得表
- `comments` - 评论表
- `favorites` - 收藏表

## 安装部署

### 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 部署步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd reading_share
```

2. **创建数据库**
```sql
-- 执行 reading_share.sql 文件创建数据库和表
mysql -u root -p < reading_share.sql
```

3. **配置数据库连接**
修改 `src/main/resources/application.properties` 文件：
```properties
spring.datasource.url=*****************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

4. **编译运行**
```bash
mvn clean package
java -jar target/reading_share-0.0.1-SNAPSHOT.jar
```

5. **访问应用**
打开浏览器访问：http://localhost:8080

### 默认账号
- 管理员账号：admin / admin123

## 使用说明

1. **注册账号**: 访问首页点击注册，填写用户信息
2. **登录系统**: 使用注册的账号登录
3. **发布心得**: 登录后点击"发布心得"，填写读书心得信息
4. **浏览心得**: 在首页或心得列表页浏览其他用户的心得
5. **评论互动**: 在心得详情页发表评论，与作者交流
6. **管理功能**: 管理员可以访问后台管理界面

## 开发说明

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 合理的分层架构

### 安全特性
- 密码BCrypt加密
- CSRF防护
- XSS防护
- 基于角色的权限控制

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证
