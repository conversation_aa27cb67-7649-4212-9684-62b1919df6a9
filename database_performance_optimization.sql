-- 数据库性能优化SQL脚本
-- 阅读心得分享平台

-- ========================================
-- 索引优化
-- ========================================

-- 1. 心得表索引优化
-- 为常用查询字段添加索引

-- 状态和创建时间复合索引（用于分页查询已发布心得）
CREATE INDEX idx_notes_status_createtime ON reading_notes(status, create_time DESC);

-- 分类ID和状态复合索引（用于按分类查询）
CREATE INDEX idx_notes_category_status ON reading_notes(category_id, status, create_time DESC);

-- 用户ID和创建时间索引（用于查询用户心得）
CREATE INDEX idx_notes_userid_createtime ON reading_notes(user_id, create_time DESC);

-- 浏览次数索引（用于热门心得排序）
CREATE INDEX idx_notes_viewcount ON reading_notes(view_count DESC);

-- 2. 评论表索引优化
-- 心得ID和创建时间复合索引（用于查询心得评论）
CREATE INDEX idx_comments_noteid_createtime ON comments(note_id, create_time ASC);

-- 父评论ID索引（用于查询回复）
CREATE INDEX idx_comments_parentid ON comments(parent_id);

-- 用户ID和创建时间索引（用于查询用户评论）
CREATE INDEX idx_comments_userid_createtime ON comments(user_id, create_time DESC);

-- 3. 用户表索引优化
-- 用户名唯一索引（登录查询）
CREATE UNIQUE INDEX idx_users_username ON users(username);

-- 邮箱索引（找回密码等功能）
CREATE INDEX idx_users_email ON users(email);

-- 启用状态索引
CREATE INDEX idx_users_enabled ON users(enabled);

-- 4. 用户角色关联表索引
-- 用户ID索引
CREATE INDEX idx_userroles_userid ON user_roles(user_id);

-- 角色ID索引
CREATE INDEX idx_userroles_roleid ON user_roles(role_id);

-- ========================================
-- 查询优化建议
-- ========================================

-- 1. 分页查询优化示例
-- 原始查询（可能较慢）：
-- SELECT * FROM reading_notes WHERE status = 'published' ORDER BY create_time DESC LIMIT 12 OFFSET 120;

-- 优化后查询（使用索引）：
-- SELECT n.*, u.username, u.nickname, c.name as category_name 
-- FROM reading_notes n 
-- USE INDEX (idx_notes_status_createtime)
-- LEFT JOIN users u ON n.user_id = u.id 
-- LEFT JOIN note_categories c ON n.category_id = c.id 
-- WHERE n.status = 'published' 
-- ORDER BY n.create_time DESC 
-- LIMIT 12 OFFSET 120;

-- 2. 分类查询优化示例
-- SELECT n.*, u.username, u.nickname, c.name as category_name 
-- FROM reading_notes n 
-- USE INDEX (idx_notes_category_status)
-- LEFT JOIN users u ON n.user_id = u.id 
-- LEFT JOIN note_categories c ON n.category_id = c.id 
-- WHERE n.category_id = ? AND n.status = 'published' 
-- ORDER BY n.create_time DESC 
-- LIMIT 12 OFFSET ?;

-- ========================================
-- 数据库配置优化
-- ========================================

-- 1. InnoDB缓冲池大小（建议设置为可用内存的70-80%）
-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 2. 查询缓存（MySQL 5.7及以下版本）
-- SET GLOBAL query_cache_size = 268435456; -- 256MB
-- SET GLOBAL query_cache_type = ON;

-- 3. 连接数配置
-- SET GLOBAL max_connections = 200;

-- 4. 慢查询日志
-- SET GLOBAL slow_query_log = ON;
-- SET GLOBAL long_query_time = 2; -- 记录执行时间超过2秒的查询

-- ========================================
-- 表结构优化建议
-- ========================================

-- 1. 心得表字段优化
-- 考虑将content字段分离到单独的表中，减少主表大小
-- CREATE TABLE reading_notes_content (
--     note_id INT PRIMARY KEY,
--     content TEXT,
--     FOREIGN KEY (note_id) REFERENCES reading_notes(id) ON DELETE CASCADE
-- );

-- 2. 添加统计字段（减少实时计算）
-- ALTER TABLE reading_notes ADD COLUMN comment_count INT DEFAULT 0;
-- ALTER TABLE users ADD COLUMN note_count INT DEFAULT 0;
-- ALTER TABLE note_categories ADD COLUMN note_count INT DEFAULT 0;

-- ========================================
-- 定期维护脚本
-- ========================================

-- 1. 分析表统计信息（建议每周执行）
-- ANALYZE TABLE reading_notes;
-- ANALYZE TABLE comments;
-- ANALYZE TABLE users;

-- 2. 优化表（建议每月执行）
-- OPTIMIZE TABLE reading_notes;
-- OPTIMIZE TABLE comments;

-- 3. 检查索引使用情况
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'reading_share'
-- ORDER BY TABLE_NAME, SEQ_IN_INDEX;

-- ========================================
-- 性能监控查询
-- ========================================

-- 1. 查看慢查询
-- SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 2. 查看索引使用情况
-- SHOW INDEX FROM reading_notes;

-- 3. 查看表状态
-- SHOW TABLE STATUS LIKE 'reading_notes';

-- 4. 查看连接状态
-- SHOW PROCESSLIST;

-- ========================================
-- 执行说明
-- ========================================

-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 建议在业务低峰期执行索引创建操作
-- 3. 创建索引可能需要较长时间，请耐心等待
-- 4. 定期监控数据库性能，根据实际情况调整配置

-- 执行完成后，建议重启应用以确保连接池配置生效
